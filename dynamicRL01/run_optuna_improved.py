import optuna
import logging
import os
import sys
import time
import json
from revamped_new import optuna_objective

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    filename='optuna_improved.log',
    format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    filemode='a'
)

def main():
    """Run Optuna hyperparameter optimization for TSNGCLEnvironment with improved features."""
    print("Starting improved Optuna hyperparameter optimization for TSNGCLEnvironment")
    logging.info("Starting improved Optuna hyperparameter optimization for TSNGCLEnvironment")
    
    # Create a new study with a different name to avoid conflicts
    study_name = "tsngcl_optimization_improved"
    storage_name = "sqlite:///optuna_improved.db"
    
    # Add pruning to stop unpromising trials early
    pruner = optuna.pruners.MedianPruner(
        n_startup_trials=10,  # Number of trials to run before pruning starts
        n_warmup_steps=20,   # Number of steps to run before pruning starts
        interval_steps=1     # Interval between pruning checks
    )
    
    try:
        # Create a new study (load_if_exists=True to continue if it exists)
        study = optuna.create_study(
            study_name=study_name,
            storage=storage_name,
            load_if_exists=True,  # Continue if it exists
            direction="maximize",  # We want to maximize the return
            pruner=pruner  # Use the pruner to stop unpromising trials early
        )
        
        print(f"Study '{study_name}' created or loaded successfully")
        logging.info(f"Study '{study_name}' created or loaded successfully")
        
        # Print study statistics if it already exists
        if len(study.trials) > 0:
            print(f"Study already has {len(study.trials)} trials")
            logging.info(f"Study already has {len(study.trials)} trials")
            
            # Print best trial so far
            print(f"Best trial so far: #{study.best_trial.number}")
            print(f"  Value: {study.best_trial.value}")
            print(f"  Params: {study.best_trial.params}")
            logging.info(f"Best trial so far: #{study.best_trial.number}")
            logging.info(f"  Value: {study.best_trial.value}")
            logging.info(f"  Params: {study.best_trial.params}")
        
        # Set the number of trials to run
        n_trials = int(input("Enter the number of trials to run (default: 20): ") or "20")
        
        # Run the optimization
        print(f"Starting optimization with {n_trials} trials")
        logging.info(f"Starting optimization with {n_trials} trials")
        
        # Add a callback to save intermediate results
        def save_intermediate_results(study, trial):
            """Save intermediate results after each trial."""
            if trial.state == optuna.trial.TrialState.COMPLETE:
                print(f"Trial #{trial.number} completed with value: {trial.value}")
                print(f"  Params: {trial.params}")
                
                # Save detailed study information to JSON for further analysis
                study_info = {
                    "best_trial": {
                        "number": study.best_trial.number,
                        "value": study.best_trial.value,
                        "params": study.best_trial.params
                    },
                    "trials": []
                }
                
                # Add information about all completed trials
                for t in study.trials:
                    if t.state == optuna.trial.TrialState.COMPLETE:
                        study_info["trials"].append({
                            "number": t.number,
                            "value": t.value,
                            "params": t.params
                        })
                
                # Save to JSON file
                with open("optuna_intermediate_results.json", "w") as f:
                    json.dump(study_info, f, indent=2)
                
                print(f"Intermediate results saved to optuna_intermediate_results.json")
        
        # Run the optimization with the callback
        study.optimize(
            optuna_objective, 
            n_trials=n_trials, 
            timeout=None,
            callbacks=[save_intermediate_results]
        )
        
        # Print optimization results
        print("Optimization completed successfully")
        print(f"Best trial: #{study.best_trial.number}")
        print(f"  Value: {study.best_trial.value}")
        print(f"  Params: {study.best_trial.params}")
        
        logging.info("Optimization completed successfully")
        logging.info(f"Best trial: #{study.best_trial.number}")
        logging.info(f"  Value: {study.best_trial.value}")
        logging.info(f"  Params: {study.best_trial.params}")
        
        # Save the best parameters to a file for easy reference
        with open("best_params_improved.txt", "w") as f:
            f.write(f"Best trial: #{study.best_trial.number}\n")
            f.write(f"Value: {study.best_trial.value}\n")
            f.write("Parameters:\n")
            for param_name, param_value in study.best_trial.params.items():
                f.write(f"  {param_name}: {param_value}\n")
        
        # Save detailed study information to JSON for further analysis
        study_info = {
            "best_trial": {
                "number": study.best_trial.number,
                "value": study.best_trial.value,
                "params": study.best_trial.params
            },
            "trials": []
        }
        
        # Add information about all trials
        for trial in study.trials:
            if trial.state == optuna.trial.TrialState.COMPLETE:
                study_info["trials"].append({
                    "number": trial.number,
                    "value": trial.value,
                    "params": trial.params
                })
        
        # Save to JSON file
        with open("optuna_results_improved.json", "w") as f:
            json.dump(study_info, f, indent=2)
        
        print("Best parameters saved to best_params_improved.txt")
        print("Detailed study results saved to optuna_results_improved.json")
        logging.info("Best parameters saved to best_params_improved.txt")
        logging.info("Detailed study results saved to optuna_results_improved.json")
        
        # Visualize the optimization results
        try:
            import matplotlib.pyplot as plt
            
            # Create plots directory if it doesn't exist
            os.makedirs("plots", exist_ok=True)
            
            # Plot optimization history
            plt.figure(figsize=(10, 6))
            optuna.visualization.matplotlib.plot_optimization_history(study)
            plt.tight_layout()
            plt.savefig("plots/optimization_history.png")
            
            # Plot parameter importances
            plt.figure(figsize=(10, 6))
            optuna.visualization.matplotlib.plot_param_importances(study)
            plt.tight_layout()
            plt.savefig("plots/param_importances.png")
            
            # Plot parallel coordinate
            plt.figure(figsize=(12, 8))
            optuna.visualization.matplotlib.plot_parallel_coordinate(study)
            plt.tight_layout()
            plt.savefig("plots/parallel_coordinate.png")
            
            print("Visualization plots saved to plots/ directory")
            logging.info("Visualization plots saved to plots/ directory")
        except Exception as e:
            print(f"Error creating visualization plots: {e}")
            logging.error(f"Error creating visualization plots: {e}")
        
    except KeyboardInterrupt:
        print("\nOptimization interrupted by user.")
        logging.info("Optimization interrupted by user.")
    except Exception as e:
        print(f"Error during optimization: {e}")
        logging.error(f"Error during optimization: {e}")
        import traceback
        traceback.print_exc()
        logging.error(traceback.format_exc())
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
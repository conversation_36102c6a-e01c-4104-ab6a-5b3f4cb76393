"""
Time-Sensitive Networking (TSN) Gate Control List (GCL) Environment

This module implements a reinforcement learning environment for optimizing
Gate Control Lists in Time-Sensitive Networking. It uses TensorFlow Agents
to train a PPO (Proximal Policy Optimization) agent that can adapt to
changing network conditions.

The environment communicates with an OMNeT++ simulation via ZeroMQ and
pauses the agent when there is no input data from the simulation.
"""

# Standard library imports
import json
import logging
import re
try:
    import signal
except ImportError:
    signal = None  # Define signal as None if import fails
import threading
import time
from collections import defaultdict
from threading import Lock

# Third-party imports
import numpy as np
import tensorflow as tf
import zmq
from tf_agents.agents.ppo import ppo_agent
from tf_agents.drivers import dynamic_step_driver
from tf_agents.environments import py_environment, tf_py_environment
from tf_agents.networks import actor_distribution_network, value_network
from tf_agents.replay_buffers import tf_uniform_replay_buffer
from tf_agents.specs import array_spec
from tf_agents.trajectories import time_step as ts
from tf_prioritized_replay_buffer import TFPrioritizedReplayBuffer
import os

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,  # <-- Change INFO to DEBUG to see debug logs
    filename='app.log',
    format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    filemode='a'
)

class TSNGCLEnvironment(py_environment.PyEnvironment):
    """Time-Sensitive Networking Gate Control List Environment.

    This environment interfaces with an OMNeT++ simulation via ZeroMQ to
    optimize Gate Control Lists for Time-Sensitive Networking. It monitors
    queue states and adjusts gate durations to optimize network performance.

    The environment uses a data_active flag to track whether data is being
    received from the simulation. When no data is received for a specified
    period, the agent is paused until data becomes available again.
    """

    def __init__(self, port=5555):
        super(TSNGCLEnvironment, self).__init__()
        logging.info("Initializing TSN GCL Environment...")
        print("Initializing TSN GCL Environment...")
        self.port = port
        # Initialize ZeroMQ context and socket with more robust settings
        try:
            # First try to clean up any existing ZMQ context
            try:
                zmq.Context.instance().term()
                time.sleep(2)  # Increased sleep time for better cleanup
                zmq._context_initialized = False
            except:
                pass

            # Create a fresh context
            self.context = zmq.Context()
            self.socket = self.context.socket(zmq.ROUTER)

            # Set socket options before binding
            self.socket.setsockopt(zmq.SNDHWM, 10000)
            self.socket.setsockopt(zmq.RCVHWM, 10000)
            self.socket.setsockopt(zmq.RCVTIMEO, 500)  # 500ms timeout for receive operations
            self.socket.setsockopt(zmq.LINGER, 0)      # Don't wait on close

            # Add socket reuse option to avoid "Address already in use" errors
            self.socket.setsockopt(zmq.RCVTIMEO, 1000)  # 1 second timeout for receive

            # Try to bind to the specified port, with fallback to random port if needed
            max_bind_attempts = 3
            bound = False
            actual_port = port

            for bind_attempt in range(max_bind_attempts):
                try:
                    # Try to bind to the specified port
                    self.socket.bind(f"tcp://*:{actual_port}")
                    bound = True
                    logging.info(f"ZeroMQ socket bound to port {actual_port} on attempt {bind_attempt+1}")
                    break
                except zmq.error.ZMQError as e:
                    if "Address already in use" in str(e) and bind_attempt < max_bind_attempts - 1:
                        logging.warning(f"Port {actual_port} already in use, trying again after cleanup")
                        # Force more aggressive cleanup
                        self.socket.close()
                        self.context.term()
                        time.sleep(3)  # Wait longer between attempts

                        # Create new context and socket
                        self.context = zmq.Context()
                        self.socket = self.context.socket(zmq.ROUTER)
                        self.socket.setsockopt(zmq.SNDHWM, 10000)
                        self.socket.setsockopt(zmq.RCVHWM, 10000)
                        self.socket.setsockopt(zmq.RCVTIMEO, 500)
                        self.socket.setsockopt(zmq.LINGER, 0)
                    else:
                        # If this is the last attempt or a different error, re-raise
                        raise

            if not bound:
                raise RuntimeError(f"Failed to bind ZeroMQ socket after {max_bind_attempts} attempts")
        except Exception as e:
            logging.error(f"Error initializing ZMQ socket: {e}")
            print(f"Error initializing ZMQ socket: {e}")
            raise

        # Define expected queues (3 switches, 3 queues each)
        self.expected_queues = [
            "S0.eth[2].macLayer.queue.queue[0]",  # Network control
            "S0.eth[2].macLayer.queue.queue[1]",  # Video
            "S0.eth[2].macLayer.queue.queue[2]",  # Best effort
            "S1.eth[1].macLayer.queue.queue[0]",  # Network control
            "S1.eth[1].macLayer.queue.queue[1]",  # Video
            "S1.eth[1].macLayer.queue.queue[2]",  # Best effort
            "S2.eth[1].macLayer.queue.queue[0]",  # Network control
            "S2.eth[1].macLayer.queue.queue[1]",  # Video
            "S2.eth[1].macLayer.queue.queue[2]",  # Best effort
        ]
        logging.info(f"Expected queues configured: {len(self.expected_queues)} queues")

        self.num_queues = len(self.expected_queues)
        self.valid_queue_pattern = re.compile(
            r"S0\.eth\[2\]\.macLayer\.queue\.queue\[[0-2]\]$|S[1-2]\.eth\[1\]\.macLayer\.queue\.queue\[[0-2]\]$"
        )

        # State management
        self.queue_states = {queue_id: {} for queue_id in self.expected_queues}
        self.state_batch = defaultdict(list)
        self.start_time = defaultdict(lambda: None)
        self.last_batch_process_time = {}  # Dictionary to track last batch processing time for each queue
        self.lock = Lock()
        self.running = True
        self.current_episode_step = 0
        self.max_episode_steps = 1000

        # GCL configuration (initial values)
        self.gcl_config = np.array([4.0, 3.0, 2.0, 1.0])  # [NC, Video, BE, All closed]
        logging.info(f"Initial GCL configuration: {self.gcl_config}")

        # Define action and observation specs, changed minimum 1 to 0
        self._action_spec = array_spec.BoundedArraySpec(
            shape=(4,), dtype=np.float32, minimum=0.0, maximum=5.0, name='action'
        )

        # Using a flat observation space instead of a dictionary
        # This simplifies the network architecture
        self._observation_spec = array_spec.BoundedArraySpec(
            shape=(self.num_queues * 3 + 4,),  # {queue_lengths + occupancy + rates}/queue + gcl
            dtype=np.float32,
            minimum=0.0,
            maximum=1000.0,
            name='observation'
        )
        self.identiy_map = {}
        logging.info("Action and observation specs defined")

        # Add data activity tracking
        self.last_data_received_time = None  # Will be set when first data is received
        self.data_active = False  # Start in inactive state until data is received
        self.data_check_interval = 1.0  # Check data activity every second

        logging.info("Data activity tracking initialized")

        # Start background thread for collecting data
        self.state_thread = threading.Thread(target=self._state_monitor)
        self.state_thread.daemon = True
        self.state_thread.start()
        logging.info("State monitor thread started")

        # Start batch processing thread for regular processing of batched data
        self.batch_thread = threading.Thread(target=self._batch_processor)
        self.batch_thread.daemon = True
        self.batch_thread.start()
        logging.info("Batch processor thread started - will process batches every 200 microseconds")
        # print("Batch processor started - will process batches every 200 microseconds")

        # Start data activity monitor thread
        self.activity_thread = threading.Thread(target=self._monitor_data_activity)
        self.activity_thread.daemon = True
        self.activity_thread.start()
        logging.info("Data activity monitor thread started with 30-second timeout")
        # print("Data activity monitor started - agent will pause if no data for 30 seconds")

        # Print ready message
        # print("TSN GCL Environment initialized and ready to accept data!")
        # print("Waiting for real data from OMNeT++ - no synthetic data will be used")
        logging.info("TSN GCL Environment initialized and ready to accept data!")
        logging.info("Waiting for real data from OMNeT++ - no synthetic data will be used")


    def _monitor_data_activity(self):
        """Monitor if data is being received from simulation.

        This method runs in a background thread and checks if data is being
        received from the OMNeT++ simulation. If no data is received for a
        specified period, it sets data_active to False, which will pause the agent.

        The timeout period is set to 30 seconds to accommodate slower data rates
        from OMNeT++ while still detecting when the simulation has truly stopped.
        """
        logging.info("Data activity monitor started")

        # Increase timeout to 30 seconds to accommodate slower data rates
        data_timeout = 30.0

        # Track state changes to avoid excessive logging
        last_state_change_time = time.time()

        while self.running:
            current_time = time.time()

            if self.last_data_received_time is not None:
                time_since_last_data = current_time - self.last_data_received_time

                # Log the time since last data every 10 seconds for debugging
                if int(current_time) % 10 == 0:
                    logging.debug(f"Time since last data: {time_since_last_data:.2f} seconds (timeout: {data_timeout} seconds)")

                # If no data for the timeout period, consider simulation inactive
                if time_since_last_data > data_timeout:
                    # Only log and change state if we're currently active
                    if self.data_active:
                        self.data_active = False
                        logging.info(f"Simulation data stream inactive after {time_since_last_data:.2f} seconds. Agent paused.")
                        # Record state change time
                        last_state_change_time = current_time
                else:
                    # Only log and change state if we're currently inactive
                    if not self.data_active:
                        self.data_active = True
                        logging.info(f"Simulation data stream active. Agent resumed after {current_time - last_state_change_time:.2f} seconds pause.")
                        # Record state change time
                        last_state_change_time = current_time
            else:
                # No data has been received yet, pause the agent
                if self.data_active:
                    self.data_active = False
                    logging.info("No initial data received. Agent paused.")
                    # Record state change time
                    last_state_change_time = current_time

                # Log waiting for initial data every 10 seconds
                if int(current_time) % 10 == 0:
                    logging.info("Waiting for initial data from OMNeT++...")

            time.sleep(self.data_check_interval)

    def action_spec(self):
        return self._action_spec

    def observation_spec(self):
        return self._observation_spec


    def _reset(self):
        """Reset the environment state."""
        logging.info("Resetting environment state...")
        self.current_episode_step = 0

        # Initialize with default values: [NC, Video, BE, guard band]
        # 4ms for NC, 3ms for Video, 2ms for BE, 1ms guard band (total 10ms cycle)
        self.gcl_config = np.array([4.0, 3.0, 2.0, 1.0])

        observation = self._get_flattened_observation()
        logging.debug(f"Environment reset. Initial observation: {observation}")
        return ts.restart(observation)

    def _get_flattened_observation(self):
        """Convert the structured observation to a flat array.

        If no valid data is available from OMNeT++, this will set data_active to False
        to indicate that the agent should pause until real data is available.
        """
        logging.debug("Generating flattened observation...")
        queue_lengths = np.zeros(self.num_queues, dtype=np.float32)
        queue_occupancy = np.zeros(self.num_queues, dtype=np.float32)
        arrival_rates = np.zeros(self.num_queues, dtype=np.float32)

        # Track if we have any valid data
        has_valid_data = False

        # Use a timeout for the lock
        lock_acquired = self.lock.acquire(timeout=1.0)  # 1 second timeout
        if lock_acquired:
            try:
                for i, queue_id in enumerate(self.expected_queues):
                    if queue_id in self.queue_states and self.queue_states[queue_id]:
                        # Extract values with default fallbacks
                        queue_lengths[i] = self.queue_states[queue_id].get('num_packets', 0.0)
                        queue_occupancy[i] = self.queue_states[queue_id].get('queue_occupancy', 0.0)
                        arrival_rates[i] = self.queue_states[queue_id].get('packetArrivalRate', 0.0)
                        has_valid_data = True
                    logging.debug(f"Queue {queue_id} state: {self.queue_states[queue_id] if queue_id in self.queue_states else 'No data'}")
            finally:
                self.lock.release()
        else:
            logging.warning("Could not acquire lock for getting flattened observation within timeout period")

        # If we don't have any valid data, log a warning but don't use default values
        if not has_valid_data:
            logging.warning("No valid queue data available - waiting for real data")
            # Set data_active to False to pause the agent
            self.data_active = False

        # Use current GCL config
        scaled_gcl = self.gcl_config

        # Ensure all arrays have the right shape before concatenation
        if len(queue_lengths) != self.num_queues:
            queue_lengths = np.zeros(self.num_queues, dtype=np.float32)
        if len(queue_occupancy) != self.num_queues:
            queue_occupancy = np.zeros(self.num_queues, dtype=np.float32)
        if len(arrival_rates) != self.num_queues:
            arrival_rates = np.zeros(self.num_queues, dtype=np.float32)

        # Ensure GCL config has the right shape (4,)
        if len(scaled_gcl) != 4:
            scaled_gcl = np.array([4.0, 3.0, 2.0, 1.0], dtype=np.float32)

        observation = np.concatenate([queue_lengths, queue_occupancy, arrival_rates, scaled_gcl]).astype(np.float32)
        logging.debug(f"Flattened observation: {observation}")
        return observation

    def _step(self, action):
        """Execute one time step within the environment.

        This method processes the agent's action and advances the environment state.
        If data_active is False (no recent data from OMNeT++), it will pause and wait
        for real data instead of using synthetic data.

        Args:
            action: The action to take in the environment

        Returns:
            A TimeStep object containing the next observation, reward, and step type
        """
        logging.info(f"Executing step {self.current_episode_step + 1} with action: {action}")

        # Check if we have active data from the simulation
        if not self.data_active:
            logging.info("No recent data from OMNeT++. Pausing agent until real data is available.")
            # print("Waiting for real data from OMNeT++ - agent paused")
            # Sleep to avoid busy waiting
            time.sleep(0.5)
            # Get current observation without updating state
            observation = self._get_flattened_observation()
            # Return the same observation with zero reward and don't advance the environment
            return ts.transition(observation, reward=0.0, discount=0.99)

        # Check if we've reached the maximum episode steps
        if self.current_episode_step >= self.max_episode_steps:
            logging.info("Max episode steps reached. Resetting environment.")
            return self.reset()

        self.current_episode_step += 1

        # Process the action
        # Only clip actions to ensure non-negative values
        clipped_action = np.maximum(action, 0.0)
        logging.debug(f"Clipped action (non-negative): {clipped_action}")

        # Directly use the agent's action as GCL config (including guard band)
        # No normalization or sum-to-10ms enforcement
        self.gcl_config = np.zeros(4, dtype=np.float32)
        self.gcl_config[:4] = clipped_action[:4]

        # Double-check all values are within bounds
        self.gcl_config = np.clip(self.gcl_config, 0.0, 5.0)

        logging.info(f"GCL config set by agent: {self.gcl_config}, Total cycle time: {np.sum(self.gcl_config)}ms")
        logging.info(f"Gate durations: NC={self.gcl_config[0]:.2f}ms, Video={self.gcl_config[1]:.2f}ms, BE={self.gcl_config[2]:.2f}ms, Guard={self.gcl_config[3]:.2f}ms")

        # Process any pending batch data before sending actions
        logging.debug("_step method called - checking for pending batch data")

        # Use a timeout for the lock
        lock_acquired = self.lock.acquire(timeout=1.0)  # 1 second timeout
        if lock_acquired:
            try:
                pending_batches = 0
                for queue_id in self.expected_queues:
                    if queue_id in self.state_batch and self.state_batch[queue_id]:
                        # Only process if we have data
                        if len(self.state_batch[queue_id]) > 0:
                            pending_batches += 1
                            logging.debug(f"Found pending batch data for queue {queue_id} with {len(self.state_batch[queue_id])} entries")

                            # Make a copy of the batch data before processing
                            batch_to_process = self.state_batch[queue_id].copy()

                            # Clear the batch before processing to avoid potential race conditions
                            self.state_batch[queue_id] = []

                            # Process the batch data outside the lock to avoid deadlocks
                            self.lock.release()
                            lock_acquired = False

                            self._handle_batch_data(queue_id, batch_to_process)
                            logging.debug(f"Finished processing pending batch data for queue {queue_id}")

                            # Reacquire the lock for the next iteration
                            lock_acquired = self.lock.acquire(timeout=1.0)
                            if not lock_acquired:
                                logging.warning("Could not reacquire lock after processing batch data")
                                break

                if pending_batches > 0:
                    logging.info(f"Processed {pending_batches} pending batches in _step method")
            finally:
                if lock_acquired:
                    self.lock.release()
        else:
            logging.warning("Could not acquire lock for processing pending batch data within timeout period")

        # Send actions after processing latest state
        for queue_id in self.expected_queues:
            self._send_action_to_omnet(queue_id, self.gcl_config)

        # Short sleep to allow for data processing
        time.sleep(0.005)

        # Get observation and calculate reward
        observation = self._get_flattened_observation()
        logging.debug(f"Observation: {observation}")
        reward = self._calculate_reward(observation)
        logging.debug(f"Step reward: {reward}")

        # Check if episode is done
        if self.current_episode_step >= self.max_episode_steps:
            logging.info("Episode terminated.")
            return ts.termination(observation, reward)
        else:
            return ts.transition(observation, reward, discount=0.99)

    def _calculate_reward(self, observation):
        # print("DEBUG: _calculate_reward called")  # <-- Add this line
        logging.debug("Calculating reward...")
        n = self.num_queues
        queue_lengths = observation[:n]
        queue_occupancy = observation[n:2*n]
        arrival_rates = observation[2*n:3*n]
        gcl_config = observation[3*n:]
        logging.debug(f"Reward debug: queue_lengths={queue_lengths}, queue_occupancy={queue_occupancy}, arrival_rates={arrival_rates}, gcl_config={gcl_config}")

        # Modify reward calculation to provide better learning signals

        # Handle negative queue occupancy values - use absolute values for penalty calculation
        abs_occupancy = np.abs(queue_occupancy)
        occupancy_penalty = -np.mean(abs_occupancy) * 0.05  # Reduced penalty

        # Normalize arrival rates to prevent extreme values from dominating
        normalized_rates = np.array([min(rate / 1000.0, 10.0) for rate in arrival_rates])
        handling_reward = np.mean(normalized_rates) * 1.0  # Keep as is

        # Prioritize different queue types with more emphasis on critical traffic
        queue_type_weights = np.array([4.0, 2.0, 1.0] * 3)

        # Reward lower queue lengths (lower is better)
        queue_length_penalty = -np.sum(queue_lengths * queue_type_weights) / 1000.0  # Less harsh

        # Penalize queue imbalance (more balanced is better)
        queue_imbalance = np.std(queue_lengths)
        imbalance_penalty = -queue_imbalance / 40.0  # Less harsh

        # Add latency reward component - extract from queue states if available
        latency_reward = 0.0
        deadline_reward = 0.0
        packet_loss_penalty = 0.0

        lock_acquired = self.lock.acquire(timeout=1.0)
        if lock_acquired:
            try:
                latencies = []
                deadline_violations = 0
                packet_losses = 0

                for queue_id in self.expected_queues:
                    if queue_id in self.queue_states:
                        if "latency" in self.queue_states[queue_id]:
                            latencies.append(self.queue_states[queue_id]["latency"])
                        if "deadline_violations" in self.queue_states[queue_id]:
                            deadline_violations += self.queue_states[queue_id]["deadline_violations"]
                        if "packet_loss" in self.queue_states[queue_id]:
                            packet_losses += self.queue_states[queue_id]["packet_loss"]

                if latencies:
                    avg_latency = np.mean(latencies)
                    latency_reward = max(0, 1.0 - (avg_latency / 10.0))

                # Reduce penalty per event
                deadline_reward = -deadline_violations * 0.05
                packet_loss_penalty = -packet_losses * 0.05

                # Clip penalties to avoid extreme negatives
                deadline_reward = np.clip(deadline_reward, -2.0, 0.0)
                packet_loss_penalty = np.clip(packet_loss_penalty, -5.0, 0.0)

            finally:
                self.lock.release()
        else:
            logging.warning("Could not acquire lock for reward calculation within timeout period")

        exploration_bonus = np.random.normal(0, 0.05)
        base_reward = 7.0  # Increased base reward

        # Dynamic weighting based on network conditions
        # If we have high queue lengths, prioritize queue management
        avg_queue_length = np.mean(queue_lengths)
        if avg_queue_length > 5.0:
            # High queue situation - focus on queue management
            weights = {
                "occupancy": 0.35,
                "handling": 0.15,
                "queue_length": 0.30,
                "imbalance": 0.20,
                "latency": 0.25,
                "deadline": 0.30,
                "packet_loss": 0.30
            }
        else:
            # Normal situation - balanced weights
            weights = {
                "occupancy": 0.25,
                "handling": 0.25,
                "queue_length": 0.20,
                "imbalance": 0.15,
                "latency": 0.20,
                "deadline": 0.25,
                "packet_loss": 0.25
            }

        # Calculate total reward with dynamic weights
        # Note: We normalize the sum of weights to ensure they add up to 1.0
        weight_sum = sum(weights.values())
        normalized_weights = {k: v/weight_sum for k, v in weights.items()}

        total_reward = (
            base_reward +                                              # Base reward
            occupancy_penalty * normalized_weights["occupancy"] +      # Occupancy weight
            handling_reward * normalized_weights["handling"] +         # Handling weight
            queue_length_penalty * normalized_weights["queue_length"] + # Queue length weight
            imbalance_penalty * normalized_weights["imbalance"] +      # Imbalance weight
            latency_reward * normalized_weights["latency"] +           # Latency weight
            deadline_reward * normalized_weights["deadline"] +         # Deadline weight
            packet_loss_penalty * normalized_weights["packet_loss"] +  # Packet loss weight
            exploration_bonus                                          # Small random noise
        )

        # Clip reward to avoid extreme values that might destabilize learning
        total_reward = np.clip(total_reward, -10.0, 10.0)

        logging.debug(f"Reward components - Base: {base_reward}, Occupancy: {occupancy_penalty}, "
                     f"Handling: {handling_reward}, Queue length: {queue_length_penalty}, "
                     f"Imbalance: {imbalance_penalty}, Latency: {latency_reward}, "
                     f"Deadline: {deadline_reward}, Packet loss: {packet_loss_penalty}, "
                     f"Exploration: {exploration_bonus}, Total: {total_reward}")

        return float(total_reward)

    def _state_monitor(self):
        """Background thread to monitor and collect state from OMNeT++."""
        logging.info("Starting state monitor...")
        poller = zmq.Poller()
        poller.register(self.socket, zmq.POLLIN)

        # Set socket options for more reliability
        self.socket.setsockopt(zmq.RCVTIMEO, 500)  # Increased timeout from 100ms to 500ms
        self.socket.setsockopt(zmq.LINGER, 0)      # Don't wait on close

        consecutive_errors = 0
        max_consecutive_errors = 5

        # Start with data_active as False until we receive data
        self.data_active = False
        self.last_data_received_time = None
        logging.info("Data stream set to inactive state until data is received.")

        # Add debug message to show socket is ready
        logging.info(f"ZMQ socket bound and ready to receive data on port 5555")
        print(f"ZMQ socket bound and ready to receive data on port 5555")

        while self.running:
            try:
                # Use a longer timeout for more reliable detection
                events = dict(poller.poll(timeout=100))  # 100ms timeout

                # Print periodic status updates
                if int(time.time()) % 10 == 0:  # Every 10 seconds
                    # print(f"State monitor active, waiting for data...")
                    logging.info("State monitor active, waiting for data...")

                if self.socket in events and events[self.socket] == zmq.POLLIN:
                    # Reset consecutive errors counter on successful poll
                    consecutive_errors = 0

                    try:
                        identity, message = self.socket.recv_multipart(zmq.NOBLOCK)
                        # Update the last data received time
                        self.last_data_received_time = time.time()

                        # Log successful data reception with timestamp
                        reception_time = time.strftime("%Y-%m-%d %H:%M:%S")
                        logging.info(f"Received data from OMNeT++ at {reception_time}")
                        # Only print to console occasionally to avoid flooding
                        if int(time.time()) % 30 == 0:  # Every 30 seconds
                            logging.info(f"Received data from OMNeT++ at {reception_time}")

                        # Check if the received message is empty
                        if not message:
                            logging.warning("Received an empty message from omnet++.")
                            continue

                        data = json.loads(message.decode())

                        if 'queue_id' in data:
                            queue_id_raw = data['queue_id']
                            if "rlTest01." in queue_id_raw:
                                queue_id = queue_id_raw.split("rlTest01.")[-1]
                            else:
                                queue_id = queue_id_raw
                            # Log queue ID at debug level to reduce verbosity
                            logging.debug(f"Received message from OMNeT++ with queue_id: {queue_id}")
                            # Only print to console occasionally
                            if int(time.time()) % 60 == 0:  # Every minute
                                logging.info(f"Processing data for queue: {queue_id}")

                            # Store identity for this queue
                            self.identiy_map[queue_id] = identity

                            # Be more lenient with queue validation
                            # Even if queue ID doesn't match expected pattern, still process it
                            if queue_id not in self.expected_queues:
                                logging.warning(f"Unexpected queue ID received: {queue_id}, but processing anyway")
                                # print(f"Unexpected queue ID received: {queue_id}, but processing anyway")
                                # Add it to expected queues to handle it in the future
                                self.expected_queues.append(queue_id)
                                self.num_queues = len(self.expected_queues)
                                self.queue_states[queue_id] = {}

                        if 'state' in data and len(data['state']) >= 5:
                            # Log state data processing at debug level
                            logging.debug(f"Processing state data for queue: {queue_id}")
                            current_time_ms = round(float(data['state'][4]) * 1000, 2)

                            # Create state entry for batch processing
                            current_state = {
                                "num_packets": float(data['state'][0]),
                                "queue_occupancy": float(data['state'][1]),
                                "packetArrivalRate": float(data['state'][2]),
                                "deadline": float(data['state'][3]),
                                "current_sim_time": current_time_ms
                            }

                            # Add to batch for this queue
                            self.state_batch[queue_id].append(current_state)
                            logging.debug(f"Added data to batch for queue {queue_id}, current size: {len(self.state_batch[queue_id])}")

                            # Get current time in microseconds
                            current_time_us = int(time.time() * 1000000)

                            # Store the current simulation time for this queue if not already set
                            if queue_id not in self.last_batch_process_time:
                                self.last_batch_process_time[queue_id] = current_time_us
                                logging.debug(f"Initialized last_batch_process_time for queue {queue_id}: {current_time_us}")

                            # Check if 200 microseconds have passed since the last batch processing for this queue
                            time_since_last_process = current_time_us - self.last_batch_process_time[queue_id]

                            # Process batch data if 200 microseconds have passed or we have enough entries
                            if time_since_last_process >= 200 or len(self.state_batch[queue_id]) >= 2:
                                # Log batch processing thresholds at debug level
                                if time_since_last_process >= 200:
                                    logging.debug(f"Time threshold reached for queue {queue_id}: {time_since_last_process} microseconds")
                                else:
                                    logging.debug(f"Batch size threshold reached for queue {queue_id} with {len(self.state_batch[queue_id])} entries")

                                # Update the last batch process time
                                self.last_batch_process_time[queue_id] = current_time_us

                                # Make a copy of the batch data before processing
                                batch_to_process = self.state_batch[queue_id].copy()

                                # Clear the batch before processing to avoid potential race conditions
                                self.state_batch[queue_id] = []

                                # Process the batch data
                                self._handle_batch_data(queue_id, batch_to_process)
                            else:
                                # Only log the data collection, don't update immediate state
                                logging.debug(f"Collected data point {len(self.state_batch[queue_id])}/2 for queue {queue_id}")

                            # Log received state data at debug level
                            logging.debug(f"Received raw state data for queue {queue_id}")
                    except zmq.ZMQError as zmq_err:
                        if zmq_err.errno == zmq.EAGAIN:
                            # Non-blocking mode returned no messages, just continue
                            pass
                        else:
                            logging.warning(f"ZMQ error in receive: {zmq_err}")
                            print(f"ZMQ error in receive: {zmq_err}")
                            consecutive_errors += 1
            except Exception as e:
                logging.error(f"Error in state monitor: {e}")
                # print(f"Error in state monitor: {e}")
                consecutive_errors += 1

                # If we get too many consecutive errors, try to reset the socket
                if consecutive_errors >= max_consecutive_errors:
                    logging.warning(f"Too many consecutive errors ({consecutive_errors}). Attempting to reset ZMQ socket.")
                    # print(f"Too many consecutive errors ({consecutive_errors}). Attempting to reset ZMQ socket.")
                    try:
                        # Close and recreate the socket
                        poller.unregister(self.socket)
                        self.socket.close()

                        # Force more aggressive cleanup
                        self.context.term()
                        time.sleep(2)  # Wait for resources to be released

                        # Create a fresh context and socket
                        self.context = zmq.Context()
                        self.socket = self.context.socket(zmq.ROUTER)

                        # Set socket options
                        self.socket.setsockopt(zmq.SNDHWM, 10000)
                        self.socket.setsockopt(zmq.RCVHWM, 10000)
                        self.socket.setsockopt(zmq.RCVTIMEO, 500)  # Increased timeout
                        self.socket.setsockopt(zmq.LINGER, 0)

                        # Try to bind with multiple attempts
                        max_bind_attempts = 3
                        bound = False

                        for bind_attempt in range(max_bind_attempts):
                            try:
                                self.socket.bind("tcp://*:5555")
                                bound = True
                                logging.info(f"ZMQ socket reset and bound successfully on attempt {bind_attempt+1}")
                                # print(f"ZMQ socket reset and bound successfully on attempt {bind_attempt+1}")
                                break
                            except zmq.error.ZMQError as e:
                                if "Address already in use" in str(e) and bind_attempt < max_bind_attempts - 1:
                                    logging.warning(f"Port 5555 still in use during reset, waiting...")
                                    time.sleep(3)  # Wait longer between attempts
                                else:
                                    # If this is the last attempt or a different error, re-raise
                                    raise

                        if not bound:
                            raise RuntimeError("Failed to bind ZMQ socket after multiple attempts during reset")

                        # Register the new socket with the poller
                        poller.register(self.socket, zmq.POLLIN)
                        consecutive_errors = 0

                        # Reset data activity state
                        self.data_active = False
                        self.last_data_received_time = None
                        logging.info("Data stream reset to inactive state until new data is received")

                    except Exception as reset_err:
                        logging.error(f"Failed to reset ZMQ socket: {reset_err}")
                        # print(f"Failed to reset ZMQ socket: {reset_err}")
                        # Continue with the current socket rather than failing completely

            # Short sleep to prevent CPU overuse
            time.sleep(0.01)  # Increased from 0.001 to 0.01 to reduce CPU usage

    def _handle_batch_data(self, queue_id, batch_data):
        """Process batch data for a queue.

        This method processes batch data for a queue and updates the queue state.
        It includes a timeout mechanism to prevent getting stuck if there's an issue.
        """
        # Log batch processing with appropriate level
        logging.debug(f"Processing batch data for queue {queue_id} with {len(batch_data) if batch_data else 0} entries")

        if not batch_data:
            logging.warning(f"Empty batch data for queue {queue_id}, skipping processing")
            return

        try:
            # Handle the case where there's only one entry in the batch
            # This might happen with the 200 microsecond time threshold
            if len(batch_data) == 1:
                logging.debug(f"Single entry batch for queue {queue_id}, using direct values")

                # Use the values directly from the single entry
                avg_num_packets = batch_data[0].get("num_packets", 0)
                avg_queue_occupancy = batch_data[0].get("queue_occupancy", 0)
                avg_packet_arrival_rate = batch_data[0].get("packetArrivalRate", 0)
            else:
                # Calculate averages from batch data for multiple entries
                avg_num_packets = np.mean([d.get("num_packets", 0) for d in batch_data])
                avg_queue_occupancy = np.mean([d.get("queue_occupancy", 0) for d in batch_data])
                avg_packet_arrival_rate = np.mean([d.get("packetArrivalRate", 0) for d in batch_data])

            # Log batch processing results at debug level
            logging.debug(f"Batch processing for {queue_id}: packets={avg_num_packets:.2f}, occupancy={avg_queue_occupancy:.2f}, rate={avg_packet_arrival_rate:.2f}")

            # Update queue states with batch-processed data - use a timeout for the lock
            lock_acquired = self.lock.acquire(timeout=1.0)  # 1 second timeout
            if lock_acquired:
                try:
                    if queue_id in self.queue_states:
                        # Calculate additional metrics for reward function
                        # Estimate latency based on queue length and arrival rate
                        # Simple model: latency = queue_length / service_rate (if service_rate > 0)
                        estimated_latency = 0.0
                        packet_loss = 0
                        deadline_violations = 0

                        # Estimate service rate as arrival rate if queue is not growing
                        # Otherwise, use a default service rate
                        if avg_packet_arrival_rate > 0 and avg_num_packets <= 0:
                            # Queue is being serviced faster than packets arrive
                            service_rate = avg_packet_arrival_rate * 1.1  # Slightly higher than arrival
                        elif avg_packet_arrival_rate > 0:
                            # Queue has packets, estimate based on arrival rate
                            service_rate = avg_packet_arrival_rate * 0.9  # Slightly lower than arrival
                        else:
                            # Default service rate if we can't estimate
                            service_rate = 1000.0  # packets per second

                        # Calculate estimated latency (in ms)
                        if service_rate > 0:
                            estimated_latency = max(0, avg_num_packets / service_rate * 1000.0)  # Convert to ms

                        # Estimate packet loss based on queue occupancy
                        # If occupancy is negative, it might indicate dropped packets
                        if avg_queue_occupancy < 0:
                            packet_loss = abs(avg_queue_occupancy)

                        # Estimate deadline violations based on latency and queue type
                        # Different queue types have different latency requirements
                        queue_type = -1
                        if "[0]" in queue_id:  # Network control
                            deadline = 1.0  # 1ms deadline
                            queue_type = 0
                        elif "[1]" in queue_id:  # Video
                            deadline = 5.0  # 5ms deadline
                            queue_type = 1
                        else:  # Best effort
                            deadline = 10.0  # 10ms deadline
                            queue_type = 2

                        # Check if estimated latency exceeds deadline
                        if estimated_latency > deadline:
                            deadline_violations = 1

                        # Update queue states with all metrics
                        self.queue_states[queue_id].update({
                            "num_packets": avg_num_packets,
                            "queue_occupancy": avg_queue_occupancy,
                            "packetArrivalRate": avg_packet_arrival_rate,
                            "latency": estimated_latency,
                            "packet_loss": packet_loss,
                            "deadline_violations": deadline_violations,
                            "queue_type": queue_type
                        })

                        # Log detailed metrics for debugging
                        if int(time.time()) % 30 == 0:  # Log every 30 seconds to avoid flooding
                            logging.info(f"Queue {queue_id} metrics - Packets: {avg_num_packets:.2f}, "
                                        f"Occupancy: {avg_queue_occupancy:.2f}, Rate: {avg_packet_arrival_rate:.2f}, "
                                        f"Latency: {estimated_latency:.2f}ms, Loss: {packet_loss}, "
                                        f"Deadline violations: {deadline_violations}")

                        logging.debug(f"Updated queue {queue_id} state with batch-processed data")
                    else:
                        logging.error(f"Queue {queue_id} not found in queue_states")
                finally:
                    self.lock.release()
            else:
                logging.warning(f"Could not acquire lock for queue {queue_id} within timeout period")

            # Signal that we've processed a batch of data
            logging.debug(f"Successfully processed batch data for queue {queue_id}")

        except Exception as e:
            logging.error(f"Error processing batch data for queue {queue_id}: {e}")
            import traceback
            logging.error(traceback.format_exc())

    def _send_action_to_omnet(self, queue_id, action):
        """Send action to OMNeT++ simulation.

        This method sends an action to the OMNeT++ simulation for a specific queue.
        It includes a timeout mechanism to prevent getting stuck if there's an issue.
        """
        # Track missing queue IDs to avoid repeated warnings
        if not hasattr(self, '_missing_queue_ids'):
            self._missing_queue_ids = set()
            self._last_missing_log_time = 0

        try:
            # Use a timeout for the lock
            lock_acquired = self.lock.acquire(timeout=1.0)  # 1 second timeout
            if lock_acquired:
                try:
                    # Check if the queue_id is in the identity map
                    if queue_id not in self.identiy_map:
                        # Only log missing queue IDs once every 30 seconds to reduce spam
                        current_time = time.time()
                        if queue_id not in self._missing_queue_ids:
                            self._missing_queue_ids.add(queue_id)
                            logging.warning(f"Queue ID not found in identity map: {queue_id}")
                        elif current_time - self._last_missing_log_time > 30:
                            # Log a summary of missing queue IDs every 30 seconds
                            missing_count = len(self._missing_queue_ids)
                            if missing_count > 0:
                                logging.warning(f"{missing_count} queue IDs not found in identity map")
                                self._last_missing_log_time = current_time
                        return

                    # Send action to OMNeT++
                    client_id = self.identiy_map[queue_id]
                    logging.debug(f"Sending action to OMNeT++ for queue {queue_id} with identity {client_id}: {action}")

                    # Set a timeout for the send operation
                    self.socket.setsockopt(zmq.SNDTIMEO, 1000)  # 1 second timeout

                    self.socket.send_multipart([
                        client_id,
                        b'',
                        json.dumps({"action": action.tolist()}).encode()
                    ])
                finally:
                    self.lock.release()
            else:
                logging.warning(f"Could not acquire lock for sending action to queue {queue_id} within timeout period")
        except zmq.ZMQError as zmq_err:
            if zmq_err.errno == zmq.EAGAIN:
                logging.warning(f"Send timeout for queue {queue_id}, OMNeT++ might be busy")
            else:
                logging.error(f"ZMQ error sending action to OMNeT++: {zmq_err}")
        except Exception as e:
            logging.error(f"Error sending action to OMNeT++: {e}")
            import traceback
            logging.error(traceback.format_exc())

    def _batch_processor(self):
        """Background thread to process batched data at regular intervals.

        This method runs in a background thread and processes batched data
        every 200 microseconds, regardless of whether new data is being received
        from OMNeT++. This ensures that the agent always has up-to-date state
        information, even when data from OMNeT++ is sparse or delayed.
        """
        logging.info("Batch processor thread started")

        while self.running:
            try:

                # Process batches for all queues
                with self.lock:
                    for queue_id in self.expected_queues:
                        if queue_id in self.state_batch and self.state_batch[queue_id]:
                            # Make a copy of the batch data before processing
                            batch_to_process = self.state_batch[queue_id].copy()

                            # Clear the batch before processing to avoid potential race conditions
                            self.state_batch[queue_id] = []

                            # Process the batch data outside the lock to avoid deadlocks
                            self.lock.release()

                            # Process the batch
                            self._handle_batch_data(queue_id, batch_to_process)
                            logging.debug(f"Batch processor processed data for queue {queue_id}")

                            # Reacquire the lock for the next iteration
                            self.lock.acquire()

                # Sleep for 200 microseconds (0.0002 seconds)
                time.sleep(0.0002)

            except Exception as e:
                logging.error(f"Error in batch processor: {e}")
                import traceback
                logging.error(traceback.format_exc())
                # Sleep briefly to avoid tight error loops
                time.sleep(0.01)

    def close(self):
        """Close the environment."""
        self.running = False
        if self.state_thread.is_alive():
            self.state_thread.join(timeout=1.0)
        if hasattr(self, 'batch_thread') and self.batch_thread.is_alive():
            self.batch_thread.join(timeout=1.0)
        if self.activity_thread.is_alive():
            self.activity_thread.join(timeout=1.0)
        self.socket.close()
        self.context.term()
        super(TSNGCLEnvironment, self).close()

class LossResponsiveLearningRateSchedule(tf.keras.optimizers.schedules.LearningRateSchedule):
    """Learning rate schedule that responds to loss explosions.

    This scheduler:
    1. Keeps the learning rate constant unless loss explodes
    2. Decays the learning rate when loss increases significantly
    3. Signals when training should be terminated if loss continues to explode
    """

    def __init__(self,
                 initial_learning_rate=1e-4,
                 decay_rate=0.5,
                 loss_increase_threshold=1.5,
                 history_size=5,
                 consecutive_explosions_limit=3):
        """Initialize the scheduler.

        Args:
            initial_learning_rate: Starting learning rate
            decay_rate: Factor to multiply learning rate by when loss explodes
            loss_increase_threshold: Ratio of current to historical loss that triggers decay
            history_size: Number of loss values to keep in history
            consecutive_explosions_limit: Number of consecutive explosions before signaling termination
        """
        super().__init__()
        self.initial_learning_rate = initial_learning_rate
        self.decay_rate = decay_rate
        self.loss_increase_threshold = loss_increase_threshold
        self.history_size = history_size
        self.consecutive_explosions_limit = consecutive_explosions_limit

        # Initialize variables
        self.current_lr = tf.Variable(initial_learning_rate, dtype=tf.float32)
        self.loss_history = []
        self.decay_count = tf.Variable(0, dtype=tf.int32)
        self.consecutive_explosions = tf.Variable(0, dtype=tf.int32)

    def __call__(self, step):
        """Return the current learning rate."""
        return self.current_lr

    def update(self, current_loss):
        """Update the learning rate based on the current loss.

        Args:
            current_loss: The current training loss

        Returns:
            True if training should be terminated, False otherwise
        """
        # Add current loss to history
        self.loss_history.append(float(current_loss))

        # Keep only the most recent losses
        if len(self.loss_history) > self.history_size:
            self.loss_history.pop(0)

        # Need at least 2 losses to detect explosion
        if len(self.loss_history) < 2:
            return False

        # Calculate average of previous losses (excluding the current one)
        prev_losses = self.loss_history[:-1]
        avg_prev_loss = sum(prev_losses) / len(prev_losses)

        # Check if current loss is significantly higher than average
        threshold_value = avg_prev_loss * self.loss_increase_threshold
        print(f"Loss explosion check: current={current_loss:.4f}, avg_prev={avg_prev_loss:.4f}, threshold={threshold_value:.4f}")
        logging.debug(f"Loss explosion check: current={current_loss:.4f}, avg_prev={avg_prev_loss:.4f}, threshold={threshold_value:.4f}")

        if current_loss > threshold_value:
            # Loss has exploded
            logging.warning(f"Loss explosion detected: current={current_loss:.4f}, avg_prev={avg_prev_loss:.4f}")
            print(f"🔥 LOSS EXPLOSION DETECTED! Current: {current_loss:.4f}, Average: {avg_prev_loss:.4f}")

            # Decay the learning rate
            new_lr = self.current_lr * self.decay_rate
            self.current_lr.assign(new_lr)
            self.decay_count.assign_add(1)

            logging.info(f"Learning rate decayed to {new_lr:.2e} (decay #{self.decay_count.numpy()})")
            print(f"📉 Learning rate decayed to {new_lr:.2e} (decay #{self.decay_count.numpy()})")

            # Increment consecutive explosions counter
            self.consecutive_explosions.assign_add(1)

            # Check if we've had too many consecutive explosions
            if self.consecutive_explosions.numpy() >= self.consecutive_explosions_limit:
                logging.warning(f"Too many consecutive loss explosions ({self.consecutive_explosions.numpy()}). Signaling to stop training.")
                return True
        else:
            # Reset consecutive explosions counter if loss is stable
            self.consecutive_explosions.assign(0)

        return False

    def reset(self):
        """Reset the scheduler to its initial state."""
        self.current_lr.assign(self.initial_learning_rate)
        self.loss_history = []
        self.decay_count.assign(0)
        self.consecutive_explosions.assign(0)
        logging.info(f"Learning rate scheduler reset to initial rate: {self.initial_learning_rate}")
        print(f"Learning rate reset to initial value: {self.initial_learning_rate}")

    def get_config(self):
        """Return configuration for serialization."""
        return {
            "initial_learning_rate": self.initial_learning_rate,
            "decay_rate": self.decay_rate,
            "loss_increase_threshold": self.loss_increase_threshold,
            "history_size": self.history_size,
            "consecutive_explosions_limit": self.consecutive_explosions_limit
        }


def create_ppo_agent(env,
                     learning_rate=5e-5,
                     optimizer_class=tf.keras.optimizers.Adam,
                     fc_layer_params=(64, 64),
                     activation_fn=tf.keras.activations.relu,
                     entropy_regularization=0.01,
                     importance_ratio_clipping=0.2,
                     num_epochs=3,
                     lambda_value=0.97,
                     discount_factor=0.995):
    """Create a PPO agent for the TSN GCL environment."""

    tf_env = tf_py_environment.TFPyEnvironment(env)

    # Create a simpler actor network
    actor_net = actor_distribution_network.ActorDistributionNetwork(
        tf_env.observation_spec(),
        tf_env.action_spec(),
        fc_layer_params=fc_layer_params,
        activation_fn=activation_fn,
        kernel_initializer=tf.keras.initializers.VarianceScaling(
            scale=1.0, mode='fan_in', distribution='truncated_normal'
        )
    )

    # Create a simpler value network
    value_net = value_network.ValueNetwork(
        tf_env.observation_spec(),
        fc_layer_params=fc_layer_params,
        activation_fn=activation_fn,
        kernel_initializer=tf.keras.initializers.VarianceScaling(
            scale=1.0, mode='fan_in', distribution='truncated_normal'
        )
    )

    # Use a custom learning rate schedule that decays only when loss is exploding
    # Optimized for lower loss and better stability
    lr_schedule = LossResponsiveLearningRateSchedule(
        initial_learning_rate=learning_rate,
        decay_rate=0.5,  # Decay by half when loss explodes
        loss_increase_threshold=1.1,  # Decay if loss increases by 10% or more (more sensitive)
        history_size=3,  # Keep track of 3 most recent loss values (shorter history)
        consecutive_explosions_limit=3  # Signal termination after 3 consecutive explosions
    )

    # Create optimizer with gradient clipping for stability
    optimizer = optimizer_class(
        learning_rate=lr_schedule,
        clipnorm=1.0  # Gradient clipping to prevent exploding gradients
    )

    agent = ppo_agent.PPOAgent(
        tf_env.time_step_spec(),
        tf_env.action_spec(),
        actor_net=actor_net,
        value_net=value_net,
        optimizer=optimizer,
        entropy_regularization=entropy_regularization,
        importance_ratio_clipping=importance_ratio_clipping,
        normalize_observations=True,
        normalize_rewards=True,
        use_gae=True,
        lambda_value=lambda_value,  # Use tuned parameter
        discount_factor=discount_factor,  # Use tuned parameter
        num_epochs=num_epochs,
        debug_summaries=True,  # Enable debug summaries
        summarize_grads_and_vars=True,  # Summarize gradients and variables
        train_step_counter=tf.Variable(0),
        compute_value_and_advantage_in_train=True  # More accurate value estimation
    )

    agent.initialize()
    return agent, tf_env, lr_schedule  # Return the scheduler so we can update it with loss values

class TSNGCLTrainer:
    def __init__(self, env, agent, tf_env, num_iterations=30, collect_steps_per_iteration=150, lr_scheduler=None):
        self.env = env
        self.agent = agent
        self.tf_env = tf_env
        self.num_iterations = num_iterations
        self.collect_steps_per_iteration = collect_steps_per_iteration
        self.lr_scheduler = lr_scheduler

        # Add minimum buffer size for training (increased for more stable training)
        self.min_buffer_size = 128  # Minimum number of frames needed for training

        # Add beta parameters for importance sampling
        self.beta_start = 0.4
        self.beta_end = 1.0

        # Initialize last_real_loss and last_avg_return
        self.last_real_loss = None
        self.last_avg_return = None

        # Early stopping parameters
        self.best_loss = float('inf')
        self.best_return = float('-inf')
        self.patience = 5  # Number of iterations to wait for improvement
        self.patience_counter = 0
        self.min_delta_loss = 0.01  # Minimum change in loss to be considered an improvement
        self.min_delta_return = 0.1  # Minimum change in return to be considered an improvement

        # # Create a replay buffer
        # self.replay_buffer = tf_uniform_replay_buffer.TFUniformReplayBuffer(
        #     data_spec=agent.collect_data_spec,
        #     batch_size=tf_env.batch_size,
        #     max_length=10000
        # )
        self.replay_buffer = TFPrioritizedReplayBuffer(
            data_spec=agent.collect_data_spec,
            batch_size=tf_env.batch_size,
            max_length=10000,
            alpha=0.6,  # Prioritization exponent
            beta=0.4,   # Initial importance sampling correction
            epsilon=1e-6  # Small constant to ensure non-zero priority
        )

        # Create a driver for collecting experience
        self.collect_driver = dynamic_step_driver.DynamicStepDriver(
            self.tf_env,
            self.agent.collect_policy,
            observers=[self.replay_buffer.add_batch],
            num_steps=collect_steps_per_iteration
        )

    def collect_data(self):
        """Collect data from the environment."""
        try:
            # Check if data is active before collecting
            if not self.env.data_active:
                print("Data stream inactive, waiting for data before collection...")
                return False

            print(f"Collecting data: {self.collect_steps_per_iteration} steps...")
            start_time = time.time()

            # Run the collect driver
            self.collect_driver.run()

            # Log collection results
            buffer_size = self.replay_buffer.num_frames()
            collection_time = time.time() - start_time
            print(f"Data collection completed in {collection_time:.2f}s. Buffer now has {buffer_size} frames.")
            logging.info(f"Data collection completed in {collection_time:.2f}s. Buffer now has {buffer_size} frames.")

            return True
        except Exception as e:
            print(f"Error during data collection: {e}")
            logging.error(f"Error during data collection: {e}")
            import traceback
            logging.error(traceback.format_exc())
            return False

    def train(self):
        """Train the agent on collected data."""
        try:
            # Check if we have enough data
            buffer_size = self.replay_buffer.num_frames()
            if buffer_size < self.min_buffer_size:
                print(f"Not enough data for training. Buffer size: {buffer_size}, minimum required: {self.min_buffer_size}")
                logging.warning(f"Not enough data for training. Buffer size: {buffer_size}, minimum required: {self.min_buffer_size}")
                return None

            print(f"Training on {buffer_size} frames...")
            start_time = time.time()

            # Sample a batch of data from the buffer
            experience = self.replay_buffer.gather_all()

            # Train the agent
            train_loss = self.agent.train(experience)

            # Log training results
            training_time = time.time() - start_time
            print(f"Training completed in {training_time:.2f}s. Loss: {train_loss.loss:.4f}")
            logging.info(f"Training completed in {training_time:.2f}s. Loss: {train_loss.loss:.4f}")

            # Update the learning rate scheduler with the current loss
            if hasattr(self, 'lr_scheduler') and self.lr_scheduler is not None:
                # Check if the learning rate scheduler signals to stop training
                stop_signal = self.lr_scheduler.update(train_loss.loss)
                if stop_signal:
                    # Ask the user what to do
                    print("\n" + "="*80)
                    print("WARNING: Loss has exploded multiple times even after learning rate decay.")
                    print(f"Current learning rate: {self.lr_scheduler.current_lr.numpy():.2e}")
                    print("Options:")
                    print("1. Save the current model and stop training")
                    print("2. Reset the experience buffer and continue with new data")
                    print("="*80)

                    choice = input("Enter your choice (1 or 2): ").strip()

                    if choice == "1":
                        print("Saving model and stopping training...")
                        logging.info("User chose to save model and stop training due to loss explosion")
                        return "SAVE_AND_STOP"
                    else:
                        print("Resetting experience buffer and continuing training...")
                        logging.info("User chose to reset experience buffer and continue training")

                        # Clear the replay buffer
                        self.replay_buffer.clear()

                        # Reset the learning rate scheduler
                        self.lr_scheduler.reset()

                        # Set the reset session flag
                        self.is_reset_session = True

                        # Return None to skip this iteration
                        return None

            return train_loss
        except Exception as e:
            print(f"Error during training: {e}")
            logging.error(f"Error during training: {e}")
            import traceback
            traceback.print_exc()
            logging.error(traceback.format_exc())

            # Return None to indicate training failed
            print("Training failed due to an error. Waiting for more data.")
            logging.error("Training failed due to an error. Waiting for more data.")
            return None

    def run_training(self):
        """Run the training loop."""
        try:
            returns = []
            losses = []
            iteration = 0
            consecutive_inactive_checks = 0  # Initialize this variable here

            print("Starting training - agent will pause when no data is available")
            logging.info("Starting training - agent will pause when no data is available")
            print(f"Initial data_active status: {self.env.data_active}")
            print(f"Number of iterations to run: {self.num_iterations}")
            logging.info(f"Initial data_active status: {self.env.data_active}")
            logging.info(f"Number of iterations to run: {self.num_iterations}")

            while iteration < self.num_iterations:
                # Check if data is active
                if not self.env.data_active:
                    consecutive_inactive_checks += 1

                    # Only print waiting message periodically to avoid spamming
                    if consecutive_inactive_checks % 10 == 0:
                        print(f"Waiting for real data from OMNeT++ for {consecutive_inactive_checks} seconds...")
                        print("The agent is paused until real data is received.")
                        logging.info(f"Waiting for real data from OMNeT++ for {consecutive_inactive_checks} seconds...")

                    time.sleep(1)  # Wait a bit before checking again
                    continue  # Skip this iteration

                # Reset counter when data becomes active
                if consecutive_inactive_checks > 0:
                    print("Real data received from OMNeT++. Resuming agent.")
                    logging.info("Real data received from OMNeT++. Resuming agent.")
                    consecutive_inactive_checks = 0

                # Collect data
                self.collect_data()

                # Train the agent
                train_loss = self.train()

                # If training returned None, skip this iteration
                if train_loss is None:
                    logging.info("Training returned None. Waiting for more data...")
                    time.sleep(1)  # Wait a bit before trying again
                    continue  # Skip to next iteration

                # If training returned "SAVE_AND_STOP", save the model and exit
                if train_loss == "SAVE_AND_STOP":
                    logging.info("Training returned SAVE_AND_STOP signal. Saving model and stopping training.")
                    print("Loss explosion detected. Saving model and stopping training as requested by user.")

                    # Save the current model
                    saved_path = self.save_model(iteration=f"loss_explosion_iter_{iteration}", returns=returns)
                    if saved_path:
                        print(f"Model saved to {saved_path}")
                        logging.info(f"Model saved to {saved_path}")

                    # Return the current returns
                    return returns

                # Record the loss
                losses.append(float(train_loss.loss))

                # Evaluation
                if iteration % 2 == 0:
                    logging.info(f"Starting evaluation at iteration {iteration}")

                    # Increase number of episodes for more stable evaluation
                    num_eval_episodes = 5  # Increased from 3

                    # Add exploration noise during evaluation to avoid getting stuck
                    # This helps the agent explore more of the state space
                    # PPOAgent doesn't have eval_policy attribute, so we'll use policy instead
                    # We'll add noise to the actions during compute_avg_return instead

                    avg_return = self.compute_avg_return(num_episodes=num_eval_episodes)

                    # If evaluation returned None, skip this part
                    if avg_return is None:
                        logging.info("Evaluation returned None. Waiting for more data...")
                        continue  # Skip to next iteration

                    # Convert to float to ensure it's a numeric value
                    try:
                        avg_return_float = float(avg_return)
                        returns.append(avg_return_float)
                        print(f'Iteration {iteration}: Average Return = {avg_return_float}, Loss = {float(train_loss.loss)}')
                    except (TypeError, ValueError) as e:
                        logging.error(f"Could not convert avg_return to float: {e}")
                        continue  # Skip to next iteration

                    # Early stopping check
                    improved = False

                    # Check if loss has improved
                    if float(train_loss.loss) < self.best_loss - self.min_delta_loss:
                        self.best_loss = float(train_loss.loss)
                        improved = True
                        logging.info(f"Loss improved to {self.best_loss}")

                    # Check if return has improved
                    if avg_return_float > self.best_return + self.min_delta_return:
                        self.best_return = avg_return_float
                        improved = True
                        logging.info(f"Return improved to {self.best_return}")

                    if improved:
                        self.patience_counter = 0
                    else:
                        self.patience_counter += 1
                        logging.info(f"No improvement for {self.patience_counter} iterations")

                    # Check if we should stop early
                    if self.patience_counter >= self.patience:
                        logging.info(f"Early stopping triggered after {iteration+1} iterations")
                        print(f"Early stopping triggered. Best loss: {self.best_loss}, Best return: {self.best_return}")
                        break

                    # Log loss history for debugging
                    if len(losses) > 1:
                        logging.info(f"Loss history (last 5): {losses[-5:]}")
                        print(f"Loss history (last 5): {losses[-5:]}")

                iteration += 1

                # Short sleep to prevent CPU overuse
                time.sleep(0.01)

            print(f"Training completed successfully after {iteration} iterations")
            print(f"Total returns collected: {len(returns)}")
            logging.info(f"Training completed successfully after {iteration} iterations")
            logging.info(f"Total returns collected: {len(returns)}")
            if returns:
                print(f"Returns: {returns}")
                logging.info(f"Returns: {returns}")

            # Disable the alarm since we completed successfully
            try:
                signal.alarm(0)
            except:
                pass  # Ignore if signal module is not available

            return returns
        except KeyboardInterrupt:
            print("Training interrupted by user.")
            logging.info("Training interrupted by user.")
            # Disable the alarm since we're exiting gracefully
            try:
                signal.alarm(0)
            except:
                pass  # Ignore if signal module is not available
        except Exception as e:
            print(f"Error during training: {e}")
            logging.error(f"Error during training: {e}")
            import traceback
            logging.error(traceback.format_exc())
            print(traceback.format_exc())
            # Disable the alarm since we're handling the error
            try:
                signal.alarm(0)
            except:
                pass  # Ignore if signal module is not available
        finally:
            print("Closing environment")
            logging.info("Closing environment")
            self.env.close()

    def compute_avg_return(self, num_episodes=5):
        """Compute average return for evaluation."""
        # Only evaluate if we have active data
        if not self.env.data_active:
            logging.info("Data stream inactive, skipping evaluation and waiting for data")
            # Return None to indicate evaluation was skipped
            return None

        logging.debug("Starting compute_avg_return...")
        total_return = 0.0
        episode_returns = []

        try:
            for episode in range(num_episodes):
                logging.debug(f"Starting evaluation episode {episode+1}/{num_episodes}")
                time_step = self.tf_env.reset()
                episode_return = 0.0
                step_count = 0

                while not time_step.is_last():
                    # If data becomes inactive during evaluation, break the loop
                    if not self.env.data_active:
                        logging.info("Data stream became inactive during evaluation, breaking loop")
                        break

                    # Get action from policy
                    action_step = self.agent.policy.action(time_step)

                    # Add small exploration noise to the action to avoid getting stuck
                    noisy_action = action_step.action.numpy() + np.random.normal(
                        0, 0.1, size=action_step.action.shape)

                    # Clip to ensure actions stay within bounds
                    action_spec = self.tf_env.action_spec()
                    noisy_action = np.clip(
                        noisy_action,
                        action_spec.minimum,
                        action_spec.maximum
                    )

                    # Use the noisy action
                    next_time_step = self.tf_env.step(noisy_action)

                    # Debug the time_step and reward
                    logging.debug(f"Time step type: {next_time_step.step_type}")
                    logging.debug(f"Raw reward: {next_time_step.reward}")

                    # Safely extract reward value
                    try:
                        if hasattr(next_time_step.reward, 'numpy'):
                            reward_array = next_time_step.reward.numpy()
                            if isinstance(reward_array, np.ndarray) and reward_array.size > 0:
                                reward = float(reward_array[0])
                            else:
                                reward = float(reward_array)
                        else:
                            reward = float(next_time_step.reward)

                        logging.debug(f"Processed reward: {reward}")
                    except Exception as e:
                        logging.warning(f"Error extracting reward: {e}. Using default value 0.0")
                        reward = 0.0

                    episode_return += reward
                    time_step = next_time_step
                    step_count += 1

                    # Limit maximum steps per episode to avoid infinite loops
                    if step_count >= 100:  # Reasonable limit for evaluation
                        logging.warning(f"Reached maximum steps ({step_count}) for evaluation episode, breaking")
                        break

                    logging.debug(f"Step {step_count}, cumulative return: {episode_return}")

                logging.info(f"Evaluation episode {episode+1} completed with return: {episode_return}")
                episode_returns.append(episode_return)
                logging.info(f"Episode return: {episode_return}")
                total_return += episode_return
                logging.info(f"Total return: {total_return}")

            if episode_returns:
                avg_return = total_return / len(episode_returns)
                logging.info(f"Computed average return: {avg_return} from {len(episode_returns)} episodes")
                self.last_avg_return = avg_return  # Store for future reference
                return avg_return
            else:
                logging.warning("No episodes completed during evaluation")
                # Return None to indicate evaluation failed
                return None

        except Exception as e:
            logging.error(f"Error during evaluation: {e}")
            import traceback
            logging.error(traceback.format_exc())
            # Return None to indicate evaluation failed
            return None

    def save_model(self, iteration=None, best_params=None, returns=None):
        """Saves the agent's policy with proper TF-Agents serialization.

        Args:
            iteration: Current iteration or a string identifier (e.g., "final", "interrupted")
            best_params: Optional dictionary of best hyperparameters
            returns: Optional list of returns during training

        Returns:
            Path to the saved model directory, or None if saving failed
        """
        model_dir = "saved_models"
        os.makedirs(model_dir, exist_ok=True)

        timestamp = time.strftime("%Y%m%d_%H%M%S")
        if isinstance(iteration, int):
            session_dir = os.path.join(model_dir, f"session_{timestamp}_iter_{iteration}")
        else:
            session_dir = os.path.join(model_dir, f"session_{timestamp}_{iteration}")

        os.makedirs(session_dir, exist_ok=True)

        try:
            # Instead of using PolicySaver, save the underlying networks directly
            # 1. Save the actor network
            actor_dir = os.path.join(session_dir, "actor_network")
            os.makedirs(actor_dir, exist_ok=True)

            # Get the actor network from the agent
            actor_network = self.agent._actor_net
            # Save the actor network using TensorFlow's SavedModel format
            tf.saved_model.save(actor_network, actor_dir)
            logging.info(f"Actor network saved to {actor_dir}")

            # 2. Save the value network
            value_dir = os.path.join(session_dir, "value_network")
            os.makedirs(value_dir, exist_ok=True)

            # Get the value network from the agent
            value_network = self.agent._value_net
            # Save the value network using TensorFlow's SavedModel format
            tf.saved_model.save(value_network, value_dir)
            logging.info(f"Value network saved to {value_dir}")

            # 3. Save additional training artifacts
            if best_params:
                with open(os.path.join(session_dir, "params.json"), 'w') as f:
                    json.dump(best_params, f, indent=4)

            if returns:
                with open(os.path.join(session_dir, "returns.json"), 'w') as f:
                    json.dump(returns, f, indent=4)

                try:
                    import matplotlib.pyplot as plt
                    plt.figure(figsize=(10, 6))
                    plt.plot(returns)
                    plt.title("Training Returns")
                    plt.xlabel("Iteration")
                    plt.ylabel("Return")
                    plt.grid(True)
                    plt.savefig(os.path.join(session_dir, "returns.png"))
                    plt.close()
                except Exception as e:
                    print(f"Couldn't save returns plot: {e}")
                    logging.warning(f"Couldn't save returns plot: {e}")

            # 4. Save the current learning rate
            if self.lr_scheduler is not None:
                current_lr = float(self.lr_scheduler.current_lr.numpy())
                with open(os.path.join(session_dir, "learning_rate.txt"), 'w') as f:
                    f.write(f"Learning rate: {current_lr}\n")
                    f.write(f"Initial learning rate: {self.lr_scheduler.initial_learning_rate}\n")
                    f.write(f"Decay count: {self.lr_scheduler.decay_count.numpy()}\n")

            # 5. Save the environment specs for future loading
            try:
                specs = {
                    'observation_spec': str(self.agent.time_step_spec().observation),
                    'action_spec': str(self.agent.action_spec()),
                    'time_step_spec': str(self.agent.time_step_spec())
                }
                with open(os.path.join(session_dir, "specs.json"), 'w') as f:
                    json.dump(specs, f, indent=4)
            except Exception as e:
                logging.warning(f"Could not save specs: {e}")

            logging.info(f"Model successfully saved to {session_dir}")
            return session_dir

        except Exception as e:
            print(f"Error saving model: {str(e)}")
            logging.error(f"Error saving model: {str(e)}")
            import traceback
            traceback.print_exc()
            logging.error(traceback.format_exc())
            return None

    def check_early_stopping(self, current_loss, current_return):
        """Check if training should be stopped early.

        Args:
            current_loss: The current training loss
            current_return: The current evaluation return

        Returns:
            True if training should be stopped, False otherwise
        """
        should_stop = False

        # Check if loss improved
        if current_loss < self.best_loss - self.min_delta_loss:
            self.best_loss = current_loss
            self.patience_counter = 0
            logging.info(f"New best loss: {current_loss:.4f}")
        # Check if return improved
        elif current_return > self.best_return + self.min_delta_return:
            self.best_return = current_return
            self.patience_counter = 0
            logging.info(f"New best return: {current_return:.4f}")
        else:
            self.patience_counter += 1
            logging.info(f"No improvement: patience counter = {self.patience_counter}/{self.patience}")

        # Check if patience exceeded
        if self.patience_counter >= self.patience:
            should_stop = True

        return should_stop

def optuna_objective(trial):
    """Objective function for Optuna hyperparameter optimization.

    This function creates an environment, trains a PPO agent with hyperparameters
    suggested by Optuna, and returns the evaluation score.

    Args:
        trial: An Optuna trial object that suggests hyperparameters

    Returns:
        Average return from evaluation (higher is better)
    """
    # Perform thorough ZMQ cleanup to avoid conflicts between trials
    try:
        import zmq
        import gc

        # First try to terminate the global context
        try:
            context = zmq.Context.instance()
            context.term()
            time.sleep(2)  # Wait for termination to complete
            zmq._context_initialized = False
        except Exception as e:
            logging.warning(f"Trial #{trial.number}: Error terminating global ZMQ context: {e}")

        # Force garbage collection to clean up any lingering ZMQ objects
        gc.collect()
        time.sleep(1)

        # Kill and recreate the ZMQ context more aggressively
        try:
            # Set _instance to None to force recreation
            if hasattr(zmq.Context, '_instance'):
                zmq.Context._instance = None

            # Create and immediately terminate a new context to reset ZMQ state
            temp_context = zmq.Context()
            temp_context.term()
            time.sleep(1)
        except Exception as e:
            logging.warning(f"Trial #{trial.number}: Error during aggressive ZMQ cleanup: {e}")

        logging.info(f"Trial #{trial.number}: Completed thorough ZMQ context cleanup")
    except Exception as e:
        logging.error(f"Trial #{trial.number}: Error during ZMQ cleanup: {e}")

    # Create environment
    env = None
    max_attempts = 3

    for attempt in range(max_attempts):
        try:
            logging.info(f"Trial #{trial.number}: Connection attempt {attempt+1}/{max_attempts}")

            # Create environment
            env = TSNGCLEnvironment(port=5555)

            logging.info(f"Trial #{trial.number}: Connection successful")

            # Wait for socket to initialize
            time.sleep(2)
            break
        except Exception as e:
            logging.error(f"Trial #{trial.number}: Error during connection attempt {attempt+1}: {e}")
            if env:
                env.close()
            env = None
            time.sleep(5)

    if env is None:
        logging.error(f"Trial #{trial.number}: Failed to create environment after multiple attempts")
        return float('-inf')  # Return worst possible score

    try:
        # Define hyperparameters to tune
        learning_rate = trial.suggest_float('learning_rate', 1e-5, 1e-3, log=True)

        # Network architecture parameters
        n_layers = trial.suggest_int('n_layers', 1, 4)  # Extended range
        layer_size = trial.suggest_categorical('layer_size', [32, 64, 128, 256])  # Different layer sizes
        fc_layer_params = tuple([layer_size] * n_layers)

        # Activation function
        activation_name = trial.suggest_categorical('activation_fn', ['relu', 'tanh', 'elu'])
        activation_fn_map = {
            'relu': tf.keras.activations.relu,
            'tanh': tf.keras.activations.tanh,
            'elu': tf.keras.activations.elu
        }
        activation_fn = activation_fn_map[activation_name]

        # PPO specific parameters
        entropy_regularization = trial.suggest_float('entropy_regularization', 0.001, 0.1, log=True)
        importance_ratio_clipping = trial.suggest_float('importance_ratio_clipping', 0.1, 0.3)
        num_epochs = trial.suggest_int('num_epochs', 2, 8)

        # GAE and discount parameters
        lambda_value = trial.suggest_float('lambda_value', 0.9, 0.99)
        discount_factor = trial.suggest_float('discount_factor', 0.95, 0.999)

        # Training parameters
        collect_steps_per_iteration = trial.suggest_categorical('collect_steps_per_iteration', [50, 100, 150, 200])
        num_iterations = trial.suggest_int('num_iterations', 10, 25)  # Reduced for optimization

        # Log the hyperparameters being tested
        logging.info(f"Trial #{trial.number}: Testing hyperparameters:")
        logging.info(f"  learning_rate={learning_rate}, n_layers={n_layers}, layer_size={layer_size}")
        logging.info(f"  activation_fn={activation_name}, entropy_reg={entropy_regularization}")
        logging.info(f"  clip_ratio={importance_ratio_clipping}, num_epochs={num_epochs}")
        logging.info(f"  lambda_value={lambda_value}, discount_factor={discount_factor}")
        logging.info(f"  collect_steps={collect_steps_per_iteration}, num_iterations={num_iterations}")

        # Create agent with tuned hyperparameters
        agent, tf_env, lr_scheduler = create_ppo_agent(
            env,
            learning_rate=learning_rate,
            fc_layer_params=fc_layer_params,
            activation_fn=activation_fn,
            entropy_regularization=entropy_regularization,
            importance_ratio_clipping=importance_ratio_clipping,
            num_epochs=num_epochs,
            lambda_value=lambda_value,
            discount_factor=discount_factor
        )

        # Create trainer with tuned parameters for evaluation
        trainer = TSNGCLTrainer(
            env=env,
            agent=agent,
            tf_env=tf_env,
            num_iterations=num_iterations,  # Use tuned parameter
            collect_steps_per_iteration=collect_steps_per_iteration,  # Use tuned parameter
            lr_scheduler=lr_scheduler  # Pass the learning rate scheduler
        )

        # Wait for data to become active before starting training
        # This is critical - we need to ensure we have data from OMNeT++
        max_wait_time = 60  # Wait up to 60 seconds for data
        wait_start = time.time()

        logging.info(f"Trial #{trial.number}: Waiting for data from OMNeT++...")

        while not env.data_active and time.time() - wait_start < max_wait_time:
            time.sleep(1)
            # Only log every 10 seconds to reduce verbosity
            if int(time.time() - wait_start) % 10 == 0 and int(time.time() - wait_start) > 0:
                logging.info(f"Trial #{trial.number}: Still waiting for data... ({int(time.time() - wait_start)}s)")

        if not env.data_active:
            logging.warning(f"Trial #{trial.number}: No data received after {max_wait_time}s. Will continue waiting for real data from OMNeT++.")
            print(f"Trial #{trial.number}: No data received after {max_wait_time}s. Will continue waiting for real data from OMNeT++.")

            # Continue waiting for real data - don't use synthetic data
            # The agent will pause until data is received from OMNeT++

            # Wait for an additional period to see if data becomes available
            additional_wait = 30  # Wait for another 30 seconds
            logging.info(f"Trial #{trial.number}: Waiting for an additional {additional_wait} seconds for data...")

            wait_start = time.time()
            while not env.data_active and time.time() - wait_start < additional_wait:
                time.sleep(1)
                # Only log every 10 seconds to reduce verbosity
                if int(time.time() - wait_start) % 10 == 0 and int(time.time() - wait_start) > 0:
                    logging.info(f"Trial #{trial.number}: Still waiting for data... ({int(time.time() - wait_start)}s)")

            if not env.data_active:
                logging.error(f"Trial #{trial.number}: No data received after extended wait period. Skipping this trial.")
                print(f"Trial #{trial.number}: No data received after extended wait period. Skipping this trial.")
                return float('-inf')  # Return worst possible score to skip this trial

        logging.info(f"Trial #{trial.number}: Data received, starting training")

        # Train the agent
        logging.info(f"Trial #{trial.number}: Starting training")
        returns = trainer.run_training()

        # Calculate score based on returns
        if returns and len(returns) > 0:
            # Use the maximum return as the score
            score = max(returns)
            logging.info(f"Trial #{trial.number}: Completed with score: {score}")
            print(f"Trial #{trial.number}: Score: {score}")
            return score
        else:
            logging.warning(f"Trial #{trial.number}: No returns recorded")
            return float('-inf')  # Return worst possible score

    except Exception as e:
        logging.error(f"Trial #{trial.number}: Error: {e}")
        import traceback
        logging.error(traceback.format_exc())
        return float('-inf')  # Return worst possible score
    finally:
        # Make sure to close the environment
        if env:
            logging.info(f"Trial #{trial.number}: Closing environment")
            env.close()

        # Perform thorough ZMQ cleanup after trial
        try:
            import zmq
            import gc

            # First ensure the environment's socket and context are properly closed
            if env:
                try:
                    if hasattr(env, 'socket') and env.socket:
                        env.socket.close()
                    if hasattr(env, 'context') and env.context:
                        env.context.term()
                except Exception as e:
                    logging.warning(f"Trial #{trial.number}: Error closing environment ZMQ resources: {e}")

            # Then terminate the global context
            try:
                context = zmq.Context.instance()
                context.term()
                time.sleep(2)
                zmq._context_initialized = False
            except Exception as e:
                logging.warning(f"Trial #{trial.number}: Error terminating global ZMQ context: {e}")

            # Force garbage collection
            gc.collect()
            time.sleep(1)

            # Reset ZMQ state more aggressively
            if hasattr(zmq.Context, '_instance'):
                zmq.Context._instance = None

            logging.info(f"Trial #{trial.number}: Final ZMQ cleanup completed")

            # Add a longer delay between trials to ensure resources are released
            time.sleep(3)

        except Exception as e:
            logging.error(f"Trial #{trial.number}: Error during final ZMQ cleanup: {e}")


if __name__ == "__main__":
    # Use TSNGCLTrainer for more comprehensive training
    print("Starting TSN GCL Environment with TSNGCLTrainer")
    logging.info("Starting TSN GCL Environment with TSNGCLTrainer")

    # Set a timeout for the entire process if signal module is available
    try:
        if signal is not None:
            def timeout_handler(*_):
                # Ignore parameters required by signal handler interface
                print("Process timed out - likely deadlocked. Exiting...")
                logging.error("Process timed out - likely deadlocked. Exiting...")
                import os
                os._exit(1)  # Force exit

            # Set a 10-minute timeout for the entire process
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(600)  # 600 seconds = 10 minutes
            print("Process timeout set to 10 minutes")
        else:
            print("Signal module not available, no timeout will be set")
    except Exception as e:
        print(f"Error setting up timeout handler: {e}")
        logging.error(f"Error setting up timeout handler: {e}")

    # Clean up any existing ZMQ context
    try:
        import zmq
        zmq.Context.instance().term()
        time.sleep(2)
        zmq._context_initialized = False
        print("Cleaned up existing ZMQ context")
        logging.info("Cleaned up existing ZMQ context")
    except Exception as e:
        print(f"Error cleaning up ZMQ context: {e}")
        logging.error(f"Error cleaning up ZMQ context: {e}")

    # Create environment with robust error handling
    max_attempts = 5
    env = None

    for attempt in range(max_attempts):
        try:
            print(f"Connection attempt {attempt+1}/{max_attempts}")
            logging.info(f"Connection attempt {attempt+1}/{max_attempts}")

            # Create environment
            env = TSNGCLEnvironment(port=5555)

            print(f"Connection attempt {attempt+1} successful")
            logging.info(f"Connection attempt {attempt+1} successful")

            # Let the data_active flag be managed by the monitor thread
            # The agent will pause until data is received

            # Wait for socket to initialize
            time.sleep(2)
            break
        except Exception as e:
            print(f"Error during connection attempt {attempt+1}: {e}")
            logging.error(f"Error during connection attempt {attempt+1}: {e}")
            if env:
                env.close()
            env = None
            time.sleep(5)

    if env is None:
        print("Failed to create environment after multiple attempts")
        logging.error("Failed to create environment after multiple attempts")
        import sys
        sys.exit(1)

    # Reset the alarm for the training phase if signal module is available
    try:
        if signal is not None:
            signal.alarm(600)  # Reset the 10-minute timeout
            print("Process timeout reset to 10 minutes for training phase")
    except Exception as e:
        print(f"Error resetting timeout: {e}")
        logging.error(f"Error resetting timeout: {e}")

    try:
        # Create a PPO agent
        print("Creating PPO agent...")
        logging.info("Creating PPO agent...")

        agent, tf_env, lr_scheduler = create_ppo_agent(env)

        # Create TSNGCLTrainer with appropriate parameters
        print("Creating TSNGCLTrainer...")
        logging.info("Creating TSNGCLTrainer...")

        # Use TSNGCLTrainer with more iterations and steps for better training
        trainer = TSNGCLTrainer(
            env=env,
            agent=agent,
            tf_env=tf_env,
            num_iterations=30,  # More iterations for better convergence
            collect_steps_per_iteration=150,  # More steps per iteration for better data collection
            lr_scheduler=lr_scheduler  # Pass the learning rate scheduler
        )

        # Set early stopping parameters
        trainer.patience = 5  # Stop if no improvement for 5 iterations
        trainer.min_delta_loss = 0.01  # Minimum improvement in loss
        trainer.min_delta_return = 0.1  # Minimum improvement in return

        print("Starting training with TSNGCLTrainer...")
        logging.info("Starting training with TSNGCLTrainer...")

        # Log debug information before starting training
        logging.info("Preparing to start training")
        logging.debug(f"Expected queues: {env.expected_queues}")
        logging.debug(f"Current queue states: {env.queue_states}")
        logging.debug(f"Current batch sizes: {[len(env.state_batch[q]) for q in env.expected_queues]}")
        logging.debug(f"Data active: {env.data_active}")
        logging.debug(f"Last data received time: {env.last_data_received_time}")

        # Log information about time-based batch processing
        logging.info("Time-based batch processing configuration:")
        logging.info(f"Batch processing interval: 200 microseconds")
        current_time_us = int(time.time() * 1000000)
        for queue_id in env.expected_queues:
            if queue_id in env.last_batch_process_time:
                time_since_last_process = current_time_us - env.last_batch_process_time[queue_id]
                logging.debug(f"Queue {queue_id}: {time_since_last_process} microseconds since last batch processing")

        # Log that we're ready to receive data
        logging.info("Environment ready to receive data from OMNeT++")
        print("Environment ready to receive data from OMNeT++")
        print("Agent will wait for real data - no default values will be used")
        logging.info("Agent will wait for real data - no default values will be used")

        # Run the training loop
        print("Starting training...")
        logging.info("Starting training loop")
        returns = trainer.run_training()
        print("Training completed")
        logging.info("Training loop completed")

        # Print training results
        if returns and len(returns) > 0:
            print(f"Training completed with final return: {returns[-1]}")
            print(f"Best return during training: {max(returns)}")
            logging.info(f"Training completed with final return: {returns[-1]}")
            logging.info(f"Best return during training: {max(returns)}")
        else:
            print("Training completed but no returns were recorded")
            logging.warning("Training completed but no returns were recorded")

    except KeyboardInterrupt:
        print("\nTraining interrupted by user.")
        logging.info("Training interrupted by user.")
    except Exception as e:
        print(f"Error during training: {e}")
        logging.error(f"Error during training: {e}")
        import traceback
        traceback.print_exc()
        logging.error(traceback.format_exc())
    finally:
        # Make sure to close the environment
        if env:
            print("Closing environment...")
            logging.info("Closing environment...")
            env.close()
            print("Environment closed.")
            logging.info("Environment closed.")
# import optuna
# import logging
# import os
# import sys
# import time
# import json
# from revamped import optuna_objective

# # Configure logging
# logging.basicConfig(
#     level=logging.INFO,
#     filename='optuna_improved.log',
#     format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
#     filemode='a'
# )

# def main():
#     """Run Optuna hyperparameter optimization for TSNGCLEnvironment with improved features."""
#     print("Starting improved Optuna hyperparameter optimization for TSNGCLEnvironment")
#     logging.info("Starting improved Optuna hyperparameter optimization for TSNGCLEnvironment")

#     # Create a new study with a different name to avoid conflicts
#     study_name = "tsngcl_optimization_improved"
#     storage_name = "sqlite:///optuna_improved.db"

#     # Add pruning to stop unpromising trials early
#     pruner = optuna.pruners.MedianPruner(
#         n_startup_trials=5,  # Number of trials to run before pruning starts
#         n_warmup_steps=10,   # Number of steps to run before pruning starts
#         interval_steps=1     # Interval between pruning checks
#     )

#     try:
#         # Create a new study (load_if_exists=True to continue if it exists)
#         study = optuna.create_study(
#             study_name=study_name,
#             storage=storage_name,
#             load_if_exists=True,  # Continue if it exists
#             direction="maximize",  # We want to maximize the return
#             pruner=pruner  # Use the pruner to stop unpromising trials early
#         )

#         print(f"Study '{study_name}' created or loaded successfully")
#         logging.info(f"Study '{study_name}' created or loaded successfully")

#         # Print study statistics if it already exists
#         if len(study.trials) > 0:
#             print(f"Study already has {len(study.trials)} trials")
#             logging.info(f"Study already has {len(study.trials)} trials")

#             # Print best trial so far
#             print(f"Best trial so far: #{study.best_trial.number}")
#             print(f"  Value: {study.best_trial.value}")
#             print(f"  Params: {study.best_trial.params}")
#             logging.info(f"Best trial so far: #{study.best_trial.number}")
#             logging.info(f"  Value: {study.best_trial.value}")
#             logging.info(f"  Params: {study.best_trial.params}")

#         # Set the number of trials to run
#         n_trials = int(input("Enter the number of trials to run (default: 20): ") or "20")

#         # Run the optimization
#         print(f"Starting optimization with {n_trials} trials")
#         logging.info(f"Starting optimization with {n_trials} trials")

#         # Add a callback to save intermediate results
#         def save_intermediate_results(study, trial):
#             """Save intermediate results after each trial."""
#             if trial.state == optuna.trial.TrialState.COMPLETE:
#                 print(f"Trial #{trial.number} completed with value: {trial.value}")
#                 print(f"  Params: {trial.params}")

#                 # Save detailed study information to JSON for further analysis
#                 study_info = {
#                     "best_trial": {
#                         "number": study.best_trial.number,
#                         "value": study.best_trial.value,
#                         "params": study.best_trial.params
#                     },
#                     "trials": []
#                 }

#                 # Add information about all completed trials
#                 for t in study.trials:
#                     if t.state == optuna.trial.TrialState.COMPLETE:
#                         study_info["trials"].append({
#                             "number": t.number,
#                             "value": t.value,
#                             "params": t.params
#                         })

#                 # Save to JSON file
#                 with open("optuna_intermediate_results.json", "w") as f:
#                     json.dump(study_info, f, indent=2)

#                 print(f"Intermediate results saved to optuna_intermediate_results.json")

#         # Run the optimization with the callback
#         study.optimize(
#             optuna_objective,
#             n_trials=n_trials,
#             timeout=None,
#             callbacks=[save_intermediate_results]
#         )

#         # Print optimization results
#         print("Optimization completed successfully")
#         print(f"Best trial: #{study.best_trial.number}")
#         print(f"  Value: {study.best_trial.value}")
#         print(f"  Params: {study.best_trial.params}")

#         logging.info("Optimization completed successfully")
#         logging.info(f"Best trial: #{study.best_trial.number}")
#         logging.info(f"  Value: {study.best_trial.value}")
#         logging.info(f"  Params: {study.best_trial.params}")

#         # Save the best parameters to a file for easy reference
#         with open("best_params_improved.txt", "w") as f:
#             f.write(f"Best trial: #{study.best_trial.number}\n")
#             f.write(f"Value: {study.best_trial.value}\n")
#             f.write("Parameters:\n")
#             for param_name, param_value in study.best_trial.params.items():
#                 f.write(f"  {param_name}: {param_value}\n")

#         # Save detailed study information to JSON for further analysis
#         study_info = {
#             "best_trial": {
#                 "number": study.best_trial.number,
#                 "value": study.best_trial.value,
#                 "params": study.best_trial.params
#             },
#             "trials": []
#         }

#         # Add information about all trials
#         for trial in study.trials:
#             if trial.state == optuna.trial.TrialState.COMPLETE:
#                 study_info["trials"].append({
#                     "number": trial.number,
#                     "value": trial.value,
#                     "params": trial.params
#                 })

#         # Save to JSON file
#         with open("optuna_results_improved.json", "w") as f:
#             json.dump(study_info, f, indent=2)

#         print("Best parameters saved to best_params_improved.txt")
#         print("Detailed study results saved to optuna_results_improved.json")
#         logging.info("Best parameters saved to best_params_improved.txt")
#         logging.info("Detailed study results saved to optuna_results_improved.json")

#         # Visualize the optimization results
#         try:
#             import matplotlib.pyplot as plt

#             # Create plots directory if it doesn't exist
#             os.makedirs("plots", exist_ok=True)

#             # Plot optimization history
#             plt.figure(figsize=(10, 6))
#             optuna.visualization.matplotlib.plot_optimization_history(study)
#             plt.tight_layout()
#             plt.savefig("plots/optimization_history.png")

#             # Plot parameter importances
#             plt.figure(figsize=(10, 6))
#             optuna.visualization.matplotlib.plot_param_importances(study)
#             plt.tight_layout()
#             plt.savefig("plots/param_importances.png")

#             # Plot parallel coordinate
#             plt.figure(figsize=(12, 8))
#             optuna.visualization.matplotlib.plot_parallel_coordinate(study)
#             plt.tight_layout()
#             plt.savefig("plots/parallel_coordinate.png")

#             print("Visualization plots saved to plots/ directory")
#             logging.info("Visualization plots saved to plots/ directory")
#         except Exception as e:
#             print(f"Error creating visualization plots: {e}")
#             logging.error(f"Error creating visualization plots: {e}")

#     except KeyboardInterrupt:
#         print("\nOptimization interrupted by user.")
#         logging.info("Optimization interrupted by user.")
#     except Exception as e:
#         print(f"Error during optimization: {e}")
#         logging.error(f"Error during optimization: {e}")
#         import traceback
#         traceback.print_exc()
#         logging.error(traceback.format_exc())
#         return 1

#     return 0

# if __name__ == "__main__":#     sys.exit(main())

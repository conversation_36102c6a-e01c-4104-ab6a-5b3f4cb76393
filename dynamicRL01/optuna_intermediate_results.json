{"best_trial": {"number": 0, "value": 733.0715831756592, "params": {"learning_rate": 6.860195138065808e-05, "n_layers": 3, "layer_size": 32, "activation_fn": "relu", "entropy_regularization": 0.00755347583137745, "importance_ratio_clipping": 0.26026623050423825, "num_epochs": 4, "lambda_value": 0.9894566013553835, "discount_factor": 0.9956974167686113, "collect_steps_per_iteration": 150, "num_iterations": 10}}, "trials": [{"number": 0, "value": 733.0715831756592, "params": {"learning_rate": 6.860195138065808e-05, "n_layers": 3, "layer_size": 32, "activation_fn": "relu", "entropy_regularization": 0.00755347583137745, "importance_ratio_clipping": 0.26026623050423825, "num_epochs": 4, "lambda_value": 0.9894566013553835, "discount_factor": 0.9956974167686113, "collect_steps_per_iteration": 150, "num_iterations": 10}}]}
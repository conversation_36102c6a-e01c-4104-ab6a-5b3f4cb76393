{"best_trial": {"number": 5, "value": 50.0, "params": {"learning_rate": 0.00010765359126370234, "n_layers": 1, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.030380514451835972, "importance_ratio_clipping": 0.1346573168568648, "num_epochs": 6, "lambda_value": 0.9815965784957017, "discount_factor": 0.9778660067940651, "collect_steps_per_iteration": 50, "num_iterations": 17}}, "trials": [{"number": 0, "value": 42.418814384937285, "params": {"learning_rate": 0.0005433902407351465, "n_layers": 3, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.001240524174608571, "importance_ratio_clipping": 0.29757343639972955, "num_epochs": 5, "lambda_value": 0.9366066907001056, "discount_factor": 0.9983936226021257, "collect_steps_per_iteration": 150, "num_iterations": 24}}, {"number": 1, "value": 49.10641325116158, "params": {"learning_rate": 1.0386433388853075e-05, "n_layers": 2, "layer_size": 32, "activation_fn": "tanh", "entropy_regularization": 0.0023033592674982805, "importance_ratio_clipping": 0.273924020828322, "num_epochs": 7, "lambda_value": 0.9655358288952807, "discount_factor": 0.9813758577671446, "collect_steps_per_iteration": 200, "num_iterations": 12}}, {"number": 2, "value": 46.78577271103859, "params": {"learning_rate": 0.0005870555720404673, "n_layers": 1, "layer_size": 64, "activation_fn": "relu", "entropy_regularization": 0.003160369574104975, "importance_ratio_clipping": 0.14893768878689317, "num_epochs": 2, "lambda_value": 0.9668947520168897, "discount_factor": 0.9598881046287853, "collect_steps_per_iteration": 50, "num_iterations": 16}}, {"number": 3, "value": 48.985874152183534, "params": {"learning_rate": 0.00022731180207927155, "n_layers": 2, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.030507407820935195, "importance_ratio_clipping": 0.21072121281608214, "num_epochs": 3, "lambda_value": 0.9885529288793529, "discount_factor": 0.9698044068253305, "collect_steps_per_iteration": 50, "num_iterations": 12}}, {"number": 4, "value": 48.69851989746094, "params": {"learning_rate": 2.705501246222236e-05, "n_layers": 1, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.0015506922851891308, "importance_ratio_clipping": 0.293804136517247, "num_epochs": 5, "lambda_value": 0.9151557317821893, "discount_factor": 0.9748189015046921, "collect_steps_per_iteration": 100, "num_iterations": 23}}, {"number": 5, "value": 50.0, "params": {"learning_rate": 0.00010765359126370234, "n_layers": 1, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.030380514451835972, "importance_ratio_clipping": 0.1346573168568648, "num_epochs": 6, "lambda_value": 0.9815965784957017, "discount_factor": 0.9778660067940651, "collect_steps_per_iteration": 50, "num_iterations": 17}}, {"number": 6, "value": 50.0, "params": {"learning_rate": 0.000706808799152042, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.041460275703966994, "importance_ratio_clipping": 0.1786052494078157, "num_epochs": 4, "lambda_value": 0.9210650435422697, "discount_factor": 0.9984032210551698, "collect_steps_per_iteration": 50, "num_iterations": 24}}, {"number": 7, "value": 47.053897404670714, "params": {"learning_rate": 1.7569409749664985e-05, "n_layers": 3, "layer_size": 128, "activation_fn": "elu", "entropy_regularization": 0.0013540695734808948, "importance_ratio_clipping": 0.12748992304810658, "num_epochs": 4, "lambda_value": 0.9768903053802511, "discount_factor": 0.9832784017257115, "collect_steps_per_iteration": 100, "num_iterations": 19}}, {"number": 8, "value": 48.345149147510526, "params": {"learning_rate": 2.602215746695363e-05, "n_layers": 4, "layer_size": 32, "activation_fn": "tanh", "entropy_regularization": 0.0013631226563592285, "importance_ratio_clipping": 0.15977661981936755, "num_epochs": 4, "lambda_value": 0.9331614788147218, "discount_factor": 0.9529274669204512, "collect_steps_per_iteration": 50, "num_iterations": 20}}, {"number": 9, "value": 48.392910248041154, "params": {"learning_rate": 0.0003200278777104607, "n_layers": 2, "layer_size": 256, "activation_fn": "relu", "entropy_regularization": 0.006883609058826466, "importance_ratio_clipping": 0.22263326109634285, "num_epochs": 2, "lambda_value": 0.9315843825417286, "discount_factor": 0.9820443899102692, "collect_steps_per_iteration": 200, "num_iterations": 25}}, {"number": 10, "value": 50.0, "params": {"learning_rate": 7.738736452284184e-05, "n_layers": 1, "layer_size": 128, "activation_fn": "tanh", "entropy_regularization": 0.07442178232504029, "importance_ratio_clipping": 0.11206566292114543, "num_epochs": 8, "lambda_value": 0.9541391359620996, "discount_factor": 0.9663926996567183, "collect_steps_per_iteration": 150, "num_iterations": 15}}, {"number": 11, "value": 50.0, "params": {"learning_rate": 8.877546471179223e-05, "n_layers": 4, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.026138821910621706, "importance_ratio_clipping": 0.17784365100932217, "num_epochs": 6, "lambda_value": 0.9038429270583328, "discount_factor": 0.9989628529186944, "collect_steps_per_iteration": 50, "num_iterations": 21}}, {"number": 12, "value": 50.0, "params": {"learning_rate": 0.0001746399563961477, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.029996234909921118, "importance_ratio_clipping": 0.18655701088470383, "num_epochs": 6, "lambda_value": 0.9193963649685688, "discount_factor": 0.9906497119132479, "collect_steps_per_iteration": 50, "num_iterations": 17}}, {"number": 13, "value": 44.55556874275207, "params": {"learning_rate": 0.0009959514887898455, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.09734825360092204, "importance_ratio_clipping": 0.23420591913834138, "num_epochs": 6, "lambda_value": 0.9484061107432084, "discount_factor": 0.9881500817586171, "collect_steps_per_iteration": 50, "num_iterations": 14}}, {"number": 14, "value": 50.0, "params": {"learning_rate": 5.1147124152952476e-05, "n_layers": 2, "layer_size": 64, "activation_fn": "relu", "entropy_regularization": 0.014759638929043983, "importance_ratio_clipping": 0.10081826902640875, "num_epochs": 4, "lambda_value": 0.9002359204122687, "discount_factor": 0.9758196263367079, "collect_steps_per_iteration": 50, "num_iterations": 22}}, {"number": 15, "value": 49.86666667461395, "params": {"learning_rate": 0.00016373790900624344, "n_layers": 4, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.05098307746203123, "importance_ratio_clipping": 0.1370908608726552, "num_epochs": 8, "lambda_value": 0.9194971588836847, "discount_factor": 0.9897503241219469, "collect_steps_per_iteration": 50, "num_iterations": 18}}, {"number": 16, "value": 45.2207917869091, "params": {"learning_rate": 5.1459864272222315e-05, "n_layers": 1, "layer_size": 128, "activation_fn": "elu", "entropy_regularization": 0.010569830941791526, "importance_ratio_clipping": 0.16301011129024096, "num_epochs": 7, "lambda_value": 0.9893901721999966, "discount_factor": 0.963825477978956, "collect_steps_per_iteration": 100, "num_iterations": 10}}, {"number": 17, "value": 49.82719916701317, "params": {"learning_rate": 0.0009942794832870081, "n_layers": 2, "layer_size": 32, "activation_fn": "elu", "entropy_regularization": 0.051174691944162264, "importance_ratio_clipping": 0.246996875042286, "num_epochs": 3, "lambda_value": 0.9625284933939715, "discount_factor": 0.953275758493097, "collect_steps_per_iteration": 200, "num_iterations": 19}}, {"number": 18, "value": 43.900422024726865, "params": {"learning_rate": 0.0003693708694497898, "n_layers": 3, "layer_size": 256, "activation_fn": "tanh", "entropy_regularization": 0.015695001491676956, "importance_ratio_clipping": 0.18958522419314433, "num_epochs": 5, "lambda_value": 0.9770977168151517, "discount_factor": 0.9938842968117179, "collect_steps_per_iteration": 150, "num_iterations": 21}}, {"number": 19, "value": 48.86592618227005, "params": {"learning_rate": 0.0001282256468096826, "n_layers": 4, "layer_size": 64, "activation_fn": "relu", "entropy_regularization": 0.0052867032107564525, "importance_ratio_clipping": 0.1303894846364157, "num_epochs": 3, "lambda_value": 0.9403379783405403, "discount_factor": 0.9789374459279206, "collect_steps_per_iteration": 50, "num_iterations": 14}}, {"number": 20, "value": 49.06125429272652, "params": {"learning_rate": 5.52316724370151e-05, "n_layers": 1, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.018122077458912894, "importance_ratio_clipping": 0.17175894016339377, "num_epochs": 7, "lambda_value": 0.9529719682480202, "discount_factor": 0.986387941662302, "collect_steps_per_iteration": 50, "num_iterations": 25}}, {"number": 21, "value": 49.31689131855965, "params": {"learning_rate": 8.944719988195528e-05, "n_layers": 1, "layer_size": 128, "activation_fn": "tanh", "entropy_regularization": 0.09399992366166746, "importance_ratio_clipping": 0.10154471812721863, "num_epochs": 8, "lambda_value": 0.9550044098418861, "discount_factor": 0.9677183037295792, "collect_steps_per_iteration": 150, "num_iterations": 15}}, {"number": 22, "value": 46.57155718207359, "params": {"learning_rate": 7.574074061439363e-05, "n_layers": 1, "layer_size": 128, "activation_fn": "tanh", "entropy_regularization": 0.05682375595207015, "importance_ratio_clipping": 0.11452163585852854, "num_epochs": 8, "lambda_value": 0.976919282913378, "discount_factor": 0.9582705098207793, "collect_steps_per_iteration": 150, "num_iterations": 17}}, {"number": 23, "value": 48.63666104674339, "params": {"learning_rate": 3.3551388032711334e-05, "n_layers": 2, "layer_size": 128, "activation_fn": "tanh", "entropy_regularization": 0.06685287614667611, "importance_ratio_clipping": 0.1421385434857119, "num_epochs": 7, "lambda_value": 0.924756188183335, "discount_factor": 0.9702483738588019, "collect_steps_per_iteration": 150, "num_iterations": 16}}, {"number": 24, "value": 48.07178091406822, "params": {"learning_rate": 0.000311673826860071, "n_layers": 1, "layer_size": 128, "activation_fn": "tanh", "entropy_regularization": 0.03858378967037883, "importance_ratio_clipping": 0.12191275845326445, "num_epochs": 5, "lambda_value": 0.9432906230109498, "discount_factor": 0.965772476296847, "collect_steps_per_iteration": 150, "num_iterations": 13}}, {"number": 25, "value": 46.86716456413269, "params": {"learning_rate": 0.00013937168579243478, "n_layers": 2, "layer_size": 128, "activation_fn": "tanh", "entropy_regularization": 0.023537684365184868, "importance_ratio_clipping": 0.15306022531940655, "num_epochs": 6, "lambda_value": 0.910351334309368, "discount_factor": 0.9604071290788871, "collect_steps_per_iteration": 150, "num_iterations": 18}}, {"number": 26, "value": 48.14711387753486, "params": {"learning_rate": 0.0005201858198488198, "n_layers": 3, "layer_size": 256, "activation_fn": "tanh", "entropy_regularization": 0.04228345101734974, "importance_ratio_clipping": 0.19913787567151636, "num_epochs": 4, "lambda_value": 0.9279322032916554, "discount_factor": 0.9720076850507162, "collect_steps_per_iteration": 100, "num_iterations": 16}}, {"number": 27, "value": 47.62360402345657, "params": {"learning_rate": 6.23774998315837e-05, "n_layers": 1, "layer_size": 32, "activation_fn": "tanh", "entropy_regularization": 0.07461950729828415, "importance_ratio_clipping": 0.11383194293147504, "num_epochs": 8, "lambda_value": 0.9586218820732121, "discount_factor": 0.9942773666657613, "collect_steps_per_iteration": 200, "num_iterations": 15}}, {"number": 28, "value": 49.33644866347313, "params": {"learning_rate": 3.964673766355006e-05, "n_layers": 2, "layer_size": 64, "activation_fn": "relu", "entropy_regularization": 0.03745812477682094, "importance_ratio_clipping": 0.14072921031369715, "num_epochs": 7, "lambda_value": 0.9729781065776438, "discount_factor": 0.9637444296356987, "collect_steps_per_iteration": 50, "num_iterations": 10}}, {"number": 29, "value": 39.426223766803744, "params": {"learning_rate": 0.0002319258488570458, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.01059841859802975, "importance_ratio_clipping": 0.16532411829711147, "num_epochs": 5, "lambda_value": 0.9486546714204147, "discount_factor": 0.978233045437553, "collect_steps_per_iteration": 150, "num_iterations": 23}}, {"number": 30, "value": 49.14228054881096, "params": {"learning_rate": 0.00011513250572876856, "n_layers": 1, "layer_size": 128, "activation_fn": "tanh", "entropy_regularization": 0.021657015762056937, "importance_ratio_clipping": 0.11339434820861766, "num_epochs": 6, "lambda_value": 0.9384339142292268, "discount_factor": 0.9559738515282977, "collect_steps_per_iteration": 150, "num_iterations": 19}}, {"number": 31, "value": 44.70255571603775, "params": {"learning_rate": 8.846727164876455e-05, "n_layers": 4, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.02740710763154765, "importance_ratio_clipping": 0.17597677240751378, "num_epochs": 6, "lambda_value": 0.9036847555009407, "discount_factor": 0.9986306093550762, "collect_steps_per_iteration": 50, "num_iterations": 21}}, {"number": 32, "value": 47.355533719062805, "params": {"learning_rate": 1.1222412325135375e-05, "n_layers": 4, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.06634854731256315, "importance_ratio_clipping": 0.18310861791106223, "num_epochs": 6, "lambda_value": 0.9071855021036516, "discount_factor": 0.9953649782523133, "collect_steps_per_iteration": 50, "num_iterations": 24}}, {"number": 33, "value": 44.986715918779375, "params": {"learning_rate": 7.270481995764307e-05, "n_layers": 4, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.04016375057090691, "importance_ratio_clipping": 0.20324826835257925, "num_epochs": 5, "lambda_value": 0.9116064217844495, "discount_factor": 0.9969665267152935, "collect_steps_per_iteration": 50, "num_iterations": 22}}, {"number": 34, "value": 41.2010419845581, "params": {"learning_rate": 0.0006574391019207364, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.02028783128239247, "importance_ratio_clipping": 0.1507461797611421, "num_epochs": 7, "lambda_value": 0.9829340190632239, "discount_factor": 0.9921813874767488, "collect_steps_per_iteration": 50, "num_iterations": 20}}, {"number": 35, "value": 50.0, "params": {"learning_rate": 0.00017497125222398308, "n_layers": 4, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.03222024780474777, "importance_ratio_clipping": 0.19742785404081614, "num_epochs": 3, "lambda_value": 0.9678860160288568, "discount_factor": 0.9983267298845023, "collect_steps_per_iteration": 50, "num_iterations": 23}}, {"number": 36, "value": 49.17076106667518, "params": {"learning_rate": 0.00010381945055154407, "n_layers": 2, "layer_size": 32, "activation_fn": "tanh", "entropy_regularization": 0.074201297270285, "importance_ratio_clipping": 0.21525412865027785, "num_epochs": 4, "lambda_value": 0.9219204957823106, "discount_factor": 0.98389203367779, "collect_steps_per_iteration": 200, "num_iterations": 24}}, {"number": 37, "value": 48.82922383546829, "params": {"learning_rate": 0.00022082858993950727, "n_layers": 1, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.002866784972499176, "importance_ratio_clipping": 0.17382886116187882, "num_epochs": 5, "lambda_value": 0.9148686949358135, "discount_factor": 0.9874753798306317, "collect_steps_per_iteration": 50, "num_iterations": 21}}, {"number": 38, "value": 44.80714680552482, "params": {"learning_rate": 2.401863176153058e-05, "n_layers": 4, "layer_size": 256, "activation_fn": "relu", "entropy_regularization": 0.007213744708919751, "importance_ratio_clipping": 0.26161955671168524, "num_epochs": 4, "lambda_value": 0.9338164801724351, "discount_factor": 0.9500687424959406, "collect_steps_per_iteration": 100, "num_iterations": 12}}, {"number": 39, "value": 49.04278916120529, "params": {"learning_rate": 3.580678409860095e-05, "n_layers": 2, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.05117830719274769, "importance_ratio_clipping": 0.12896292260564463, "num_epochs": 2, "lambda_value": 0.9833648554161056, "discount_factor": 0.9741119951865314, "collect_steps_per_iteration": 50, "num_iterations": 17}}, {"number": 40, "value": 46.83982920050621, "params": {"learning_rate": 1.4250442333849013e-05, "n_layers": 3, "layer_size": 128, "activation_fn": "elu", "entropy_regularization": 0.02964632515706054, "importance_ratio_clipping": 0.15396007806955547, "num_epochs": 6, "lambda_value": 0.9012634119018718, "discount_factor": 0.9918252833832623, "collect_steps_per_iteration": 50, "num_iterations": 15}}, {"number": 41, "value": 48.731530332565306, "params": {"learning_rate": 0.00018371929963920916, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.02561064244280395, "importance_ratio_clipping": 0.18632112386665275, "num_epochs": 6, "lambda_value": 0.9175391630386942, "discount_factor": 0.9903794527742753, "collect_steps_per_iteration": 50, "num_iterations": 16}}, {"number": 42, "value": 50.0, "params": {"learning_rate": 7.748609655184304e-05, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.01633300978038327, "importance_ratio_clipping": 0.2248454758810775, "num_epochs": 6, "lambda_value": 0.9068617304773248, "discount_factor": 0.9952675793619603, "collect_steps_per_iteration": 50, "num_iterations": 17}}, {"number": 43, "value": 48.562389463186264, "params": {"learning_rate": 0.00013474554487653685, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.013070727382138108, "importance_ratio_clipping": 0.2096144456642786, "num_epochs": 7, "lambda_value": 0.9250016098803132, "discount_factor": 0.9844007432173083, "collect_steps_per_iteration": 50, "num_iterations": 18}}, {"number": 44, "value": 49.255757093429565, "params": {"learning_rate": 0.0007488658999905325, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.0318214896590801, "importance_ratio_clipping": 0.18041356325241648, "num_epochs": 4, "lambda_value": 0.9298247977105906, "discount_factor": 0.9808189568661274, "collect_steps_per_iteration": 50, "num_iterations": 20}}, {"number": 45, "value": 43.730447167158125, "params": {"learning_rate": 4.491120215128322e-05, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.0010469950966507798, "importance_ratio_clipping": 0.19097679692340122, "num_epochs": 5, "lambda_value": 0.9127655515931363, "discount_factor": 0.9963383378019758, "collect_steps_per_iteration": 50, "num_iterations": 14}}, {"number": 46, "value": 50.0, "params": {"learning_rate": 0.0004430539791713144, "n_layers": 2, "layer_size": 32, "activation_fn": "relu", "entropy_regularization": 0.08505159430596253, "importance_ratio_clipping": 0.29440992010473105, "num_epochs": 7, "lambda_value": 0.9188438490546406, "discount_factor": 0.9922011320506656, "collect_steps_per_iteration": 200, "num_iterations": 19}}, {"number": 47, "value": 49.29794215559959, "params": {"learning_rate": 0.0002947096873003997, "n_layers": 4, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.04801778341308035, "importance_ratio_clipping": 0.16333066067425217, "num_epochs": 8, "lambda_value": 0.9345492299313026, "discount_factor": 0.9989377900639915, "collect_steps_per_iteration": 50, "num_iterations": 25}}, {"number": 48, "value": 50.0, "params": {"learning_rate": 6.107734592196491e-05, "n_layers": 1, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.035746771564593216, "importance_ratio_clipping": 0.13582733018995063, "num_epochs": 6, "lambda_value": 0.9705839277153497, "discount_factor": 0.9897527730423759, "collect_steps_per_iteration": 100, "num_iterations": 22}}, {"number": 49, "value": 50.0, "params": {"learning_rate": 0.00010972345701206023, "n_layers": 2, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.06172576215843055, "importance_ratio_clipping": 0.10677506304346433, "num_epochs": 5, "lambda_value": 0.9474487521687168, "discount_factor": 0.9862856438122395, "collect_steps_per_iteration": 50, "num_iterations": 15}}, {"number": 50, "value": 47.644953548908234, "params": {"learning_rate": 0.0002401950889858116, "n_layers": 4, "layer_size": 128, "activation_fn": "elu", "entropy_regularization": 0.047981233535830656, "importance_ratio_clipping": 0.24630861492700934, "num_epochs": 3, "lambda_value": 0.9634406256983533, "discount_factor": 0.9934411372572385, "collect_steps_per_iteration": 150, "num_iterations": 13}}, {"number": 51, "value": 48.382068699598314, "params": {"learning_rate": 4.9463711474086335e-05, "n_layers": 2, "layer_size": 64, "activation_fn": "relu", "entropy_regularization": 0.013497516253744455, "importance_ratio_clipping": 0.12116569533254187, "num_epochs": 4, "lambda_value": 0.9023171460699592, "discount_factor": 0.9759226255308626, "collect_steps_per_iteration": 50, "num_iterations": 23}}, {"number": 52, "value": 49.48808925747871, "params": {"learning_rate": 9.019200661769337e-05, "n_layers": 1, "layer_size": 64, "activation_fn": "relu", "entropy_regularization": 0.020034656949086797, "importance_ratio_clipping": 0.10057360942874186, "num_epochs": 4, "lambda_value": 0.9058679121426013, "discount_factor": 0.9774350270715242, "collect_steps_per_iteration": 50, "num_iterations": 22}}, {"number": 53, "value": 49.10778964161873, "params": {"learning_rate": 0.00015553877495021596, "n_layers": 1, "layer_size": 64, "activation_fn": "relu", "entropy_regularization": 0.00829677527762527, "importance_ratio_clipping": 0.12274267351313162, "num_epochs": 3, "lambda_value": 0.9087686601960214, "discount_factor": 0.9678145955581116, "collect_steps_per_iteration": 50, "num_iterations": 18}}, {"number": 54, "value": 50.0, "params": {"learning_rate": 6.572189745127855e-05, "n_layers": 3, "layer_size": 64, "activation_fn": "relu", "entropy_regularization": 0.014421040233413642, "importance_ratio_clipping": 0.10757688174028132, "num_epochs": 5, "lambda_value": 0.9153512203692175, "discount_factor": 0.9737909414656584, "collect_steps_per_iteration": 50, "num_iterations": 17}}, {"number": 55, "value": 50.0, "params": {"learning_rate": 2.936957507862544e-05, "n_layers": 2, "layer_size": 64, "activation_fn": "relu", "entropy_regularization": 0.024943055449948796, "importance_ratio_clipping": 0.1576984890934836, "num_epochs": 4, "lambda_value": 0.9003548838980382, "discount_factor": 0.9809145806313603, "collect_steps_per_iteration": 150, "num_iterations": 24}}, {"number": 56, "value": 49.09087129831314, "params": {"learning_rate": 2.129504732015389e-05, "n_layers": 1, "layer_size": 128, "activation_fn": "tanh", "entropy_regularization": 0.005350420337406575, "importance_ratio_clipping": 0.1438979308056924, "num_epochs": 7, "lambda_value": 0.941855833191027, "discount_factor": 0.9714143492749087, "collect_steps_per_iteration": 50, "num_iterations": 21}}, {"number": 57, "value": 49.42676280140877, "params": {"learning_rate": 4.476051561728502e-05, "n_layers": 1, "layer_size": 32, "activation_fn": "tanh", "entropy_regularization": 0.018688119979301747, "importance_ratio_clipping": 0.13231564768126128, "num_epochs": 6, "lambda_value": 0.9223180819522656, "discount_factor": 0.964694013878923, "collect_steps_per_iteration": 100, "num_iterations": 20}}, {"number": 58, "value": 49.25261102318764, "params": {"learning_rate": 0.0008524789896904726, "n_layers": 2, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.04198721536069547, "importance_ratio_clipping": 0.19404166962470415, "num_epochs": 8, "lambda_value": 0.9534245455669407, "discount_factor": 0.9754822434445016, "collect_steps_per_iteration": 200, "num_iterations": 16}}, {"number": 59, "value": 47.00616388916969, "params": {"learning_rate": 8.314085430952696e-05, "n_layers": 3, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.01136112261569867, "importance_ratio_clipping": 0.11800944075984927, "num_epochs": 5, "lambda_value": 0.9599684537974856, "discount_factor": 0.972637002418948, "collect_steps_per_iteration": 150, "num_iterations": 24}}, {"number": 60, "value": 46.16155630350113, "params": {"learning_rate": 0.00011555659023436543, "n_layers": 1, "layer_size": 128, "activation_fn": "relu", "entropy_regularization": 0.01656118664441649, "importance_ratio_clipping": 0.16880994737277208, "num_epochs": 7, "lambda_value": 0.9264590169137124, "discount_factor": 0.9618174785938653, "collect_steps_per_iteration": 50, "num_iterations": 22}}, {"number": 61, "value": 49.31957603693009, "params": {"learning_rate": 0.00017284606480708647, "n_layers": 4, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.03191698704001828, "importance_ratio_clipping": 0.20238660678610226, "num_epochs": 3, "lambda_value": 0.9808236374336253, "discount_factor": 0.9980637153804867, "collect_steps_per_iteration": 50, "num_iterations": 23}}, {"number": 62, "value": 48.590709471702574, "params": {"learning_rate": 0.00020643196980523338, "n_layers": 4, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.0338369285588142, "importance_ratio_clipping": 0.19791372518063066, "num_epochs": 2, "lambda_value": 0.9685723994118614, "discount_factor": 0.995312850211888, "collect_steps_per_iteration": 50, "num_iterations": 25}}, {"number": 63, "value": 45.37768404483795, "params": {"learning_rate": 0.00014834331973577673, "n_layers": 4, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.02357026674281068, "importance_ratio_clipping": 0.17730063390071102, "num_epochs": 3, "lambda_value": 0.9751518733216957, "discount_factor": 0.9677584383855067, "collect_steps_per_iteration": 50, "num_iterations": 23}}, {"number": 64, "value": 42.7191876411438, "params": {"learning_rate": 0.0002813026704606125, "n_layers": 4, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.02907656449965173, "importance_ratio_clipping": 0.10818461390508961, "num_epochs": 3, "lambda_value": 0.9867009676184287, "discount_factor": 0.9972300109458211, "collect_steps_per_iteration": 50, "num_iterations": 22}}, {"number": 65, "value": 47.93474324941635, "params": {"learning_rate": 0.0004360807772130654, "n_layers": 4, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.058705866631530226, "importance_ratio_clipping": 0.21328759110115147, "num_epochs": 4, "lambda_value": 0.9041021898733556, "discount_factor": 0.9798566301676951, "collect_steps_per_iteration": 50, "num_iterations": 15}}, {"number": 66, "value": 48.40292786955833, "params": {"learning_rate": 0.00012728305194668722, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.044515029987661596, "importance_ratio_clipping": 0.2212316220601883, "num_epochs": 6, "lambda_value": 0.9665595633497566, "discount_factor": 0.9691994371229957, "collect_steps_per_iteration": 150, "num_iterations": 18}}, {"number": 67, "value": 45.156784975528716, "params": {"learning_rate": 9.484893373932513e-05, "n_layers": 4, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.07915351266556854, "importance_ratio_clipping": 0.14602508206514447, "num_epochs": 2, "lambda_value": 0.937585281920327, "discount_factor": 0.9936020467042556, "collect_steps_per_iteration": 50, "num_iterations": 19}}, {"number": 68, "value": 48.98091734647751, "params": {"learning_rate": 7.251314254604853e-05, "n_layers": 1, "layer_size": 128, "activation_fn": "elu", "entropy_regularization": 0.09630743953392792, "importance_ratio_clipping": 0.1854182719653966, "num_epochs": 4, "lambda_value": 0.9789729223226548, "discount_factor": 0.9967663448195405, "collect_steps_per_iteration": 50, "num_iterations": 16}}, {"number": 69, "value": 48.8793308019638, "params": {"learning_rate": 5.6345748420529676e-05, "n_layers": 3, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.0220562556122733, "importance_ratio_clipping": 0.20623836629542422, "num_epochs": 3, "lambda_value": 0.9590963469364999, "discount_factor": 0.9772199428155041, "collect_steps_per_iteration": 200, "num_iterations": 25}}, {"number": 70, "value": 46.94773486852646, "params": {"learning_rate": 0.0003778767096691857, "n_layers": 2, "layer_size": 32, "activation_fn": "elu", "entropy_regularization": 0.001816054314230855, "importance_ratio_clipping": 0.19434159837743156, "num_epochs": 5, "lambda_value": 0.9106450385132491, "discount_factor": 0.9824114289179191, "collect_steps_per_iteration": 100, "num_iterations": 23}}, {"number": 71, "value": 50.0, "params": {"learning_rate": 7.678829038608392e-05, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.016702377155935155, "importance_ratio_clipping": 0.2531645320249275, "num_epochs": 6, "lambda_value": 0.9070706697389197, "discount_factor": 0.9947993959034513, "collect_steps_per_iteration": 50, "num_iterations": 17}}, {"number": 72, "value": 49.186636340618136, "params": {"learning_rate": 6.640255781705897e-05, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.009270082236203037, "importance_ratio_clipping": 0.23387783745399662, "num_epochs": 6, "lambda_value": 0.905141445919733, "discount_factor": 0.997920247652638, "collect_steps_per_iteration": 50, "num_iterations": 17}}, {"number": 73, "value": 46.90748376846314, "params": {"learning_rate": 9.825488237941503e-05, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.026639670190944105, "importance_ratio_clipping": 0.28380578044961285, "num_epochs": 6, "lambda_value": 0.9000883550063297, "discount_factor": 0.9956497586333971, "collect_steps_per_iteration": 50, "num_iterations": 17}}, {"number": 74, "value": 48.82524937987328, "params": {"learning_rate": 0.00011995724300891033, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.012334528385260946, "importance_ratio_clipping": 0.23165855067656982, "num_epochs": 7, "lambda_value": 0.9136196020940671, "discount_factor": 0.9920990889784744, "collect_steps_per_iteration": 50, "num_iterations": 14}}, {"number": 75, "value": 45.1784524679184, "params": {"learning_rate": 5.199669144473527e-05, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.035814978025762764, "importance_ratio_clipping": 0.1703055897330868, "num_epochs": 5, "lambda_value": 0.9559688306407268, "discount_factor": 0.9965901746961344, "collect_steps_per_iteration": 50, "num_iterations": 16}}, {"number": 76, "value": 47.66132430434227, "params": {"learning_rate": 3.734253984993667e-05, "n_layers": 4, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.015313350270842428, "importance_ratio_clipping": 0.2219989005567327, "num_epochs": 6, "lambda_value": 0.9508400574059237, "discount_factor": 0.9907482368024162, "collect_steps_per_iteration": 50, "num_iterations": 20}}, {"number": 77, "value": 49.32804122567177, "params": {"learning_rate": 7.860268003138856e-05, "n_layers": 2, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.018210167333002916, "importance_ratio_clipping": 0.17915232762074673, "num_epochs": 6, "lambda_value": 0.9076631769376057, "discount_factor": 0.9887435294676135, "collect_steps_per_iteration": 150, "num_iterations": 18}}, {"number": 78, "value": 44.75838780403137, "params": {"learning_rate": 0.00025675194037338853, "n_layers": 3, "layer_size": 128, "activation_fn": "relu", "entropy_regularization": 0.054998328634532745, "importance_ratio_clipping": 0.22697833268067202, "num_epochs": 7, "lambda_value": 0.9720513123952007, "discount_factor": 0.9989716267022747, "collect_steps_per_iteration": 50, "num_iterations": 24}}, {"number": 79, "value": 50.0, "params": {"learning_rate": 0.0001809552306678712, "n_layers": 4, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.029232456924886324, "importance_ratio_clipping": 0.24247719791028716, "num_epochs": 5, "lambda_value": 0.9094434908226416, "discount_factor": 0.9575005559841376, "collect_steps_per_iteration": 50, "num_iterations": 19}}, {"number": 80, "value": 48.87687128782272, "params": {"learning_rate": 0.00020098203332274912, "n_layers": 1, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.039713496460009476, "importance_ratio_clipping": 0.11432253494569537, "num_epochs": 8, "lambda_value": 0.9167866207595518, "discount_factor": 0.9942697046309478, "collect_steps_per_iteration": 50, "num_iterations": 11}}, {"number": 81, "value": 46.56357416510582, "params": {"learning_rate": 0.0005557747308398954, "n_layers": 2, "layer_size": 32, "activation_fn": "relu", "entropy_regularization": 0.07165642341733593, "importance_ratio_clipping": 0.2916961535495851, "num_epochs": 7, "lambda_value": 0.9209275470788953, "discount_factor": 0.9928996681193518, "collect_steps_per_iteration": 200, "num_iterations": 21}}, {"number": 82, "value": 48.851727336645126, "params": {"learning_rate": 0.0008996663026932176, "n_layers": 2, "layer_size": 32, "activation_fn": "relu", "entropy_regularization": 0.08299575342700503, "importance_ratio_clipping": 0.2677406944763395, "num_epochs": 8, "lambda_value": 0.9625785635211979, "discount_factor": 0.9859014966432366, "collect_steps_per_iteration": 200, "num_iterations": 19}}, {"number": 83, "value": 50.0, "params": {"learning_rate": 0.00045331219905401365, "n_layers": 2, "layer_size": 32, "activation_fn": "relu", "entropy_regularization": 0.08884287155125298, "importance_ratio_clipping": 0.1887939946658816, "num_epochs": 7, "lambda_value": 0.9188584421088074, "discount_factor": 0.988564180985003, "collect_steps_per_iteration": 200, "num_iterations": 19}}, {"number": 84, "value": 48.557559156417845, "params": {"learning_rate": 0.0007366627012752819, "n_layers": 2, "layer_size": 32, "activation_fn": "relu", "entropy_regularization": 0.06369395331776019, "importance_ratio_clipping": 0.27966051325964886, "num_epochs": 6, "lambda_value": 0.9303697718564963, "discount_factor": 0.9955368148103422, "collect_steps_per_iteration": 200, "num_iterations": 21}}, {"number": 85, "value": 50.0, "params": {"learning_rate": 0.0003418089123146988, "n_layers": 3, "layer_size": 32, "activation_fn": "relu", "entropy_regularization": 0.04497944155022941, "importance_ratio_clipping": 0.10392432317657134, "num_epochs": 8, "lambda_value": 0.9454708210931422, "discount_factor": 0.9913243224332994, "collect_steps_per_iteration": 200, "num_iterations": 18}}, {"number": 86, "value": 49.56042695641518, "params": {"learning_rate": 0.0006667113472423827, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.02129557624149742, "importance_ratio_clipping": 0.12446435744642607, "num_epochs": 6, "lambda_value": 0.901981167316112, "discount_factor": 0.994224956282826, "collect_steps_per_iteration": 150, "num_iterations": 22}}, {"number": 87, "value": 50.0, "params": {"learning_rate": 0.0004906491576539078, "n_layers": 1, "layer_size": 128, "activation_fn": "relu", "entropy_regularization": 0.05520925090695605, "importance_ratio_clipping": 0.1658685598875764, "num_epochs": 7, "lambda_value": 0.9124181118036354, "discount_factor": 0.997639036382378, "collect_steps_per_iteration": 50, "num_iterations": 20}}, {"number": 88, "value": 48.729067265987396, "params": {"learning_rate": 0.00010692389643623895, "n_layers": 2, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.024741086464757683, "importance_ratio_clipping": 0.1822779026297103, "num_epochs": 6, "lambda_value": 0.9231062809969195, "discount_factor": 0.9926631726818157, "collect_steps_per_iteration": 100, "num_iterations": 16}}, {"number": 89, "value": 48.29559311270714, "params": {"learning_rate": 4.3466227322971414e-05, "n_layers": 1, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.032191616212979356, "importance_ratio_clipping": 0.1598982224518909, "num_epochs": 3, "lambda_value": 0.9881069426547789, "discount_factor": 0.9964827015264678, "collect_steps_per_iteration": 50, "num_iterations": 23}}, {"number": 90, "value": 46.324811989068984, "params": {"learning_rate": 6.0884868069410226e-05, "n_layers": 4, "layer_size": 64, "activation_fn": "relu", "entropy_regularization": 0.05072225767960819, "importance_ratio_clipping": 0.19673627108827263, "num_epochs": 4, "lambda_value": 0.9027193176581914, "discount_factor": 0.998952043073869, "collect_steps_per_iteration": 50, "num_iterations": 17}}, {"number": 91, "value": 49.187014639377594, "params": {"learning_rate": 6.0383248804035366e-05, "n_layers": 1, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.03851885534773364, "importance_ratio_clipping": 0.13593300710118933, "num_epochs": 6, "lambda_value": 0.9695143025215035, "discount_factor": 0.9898572859671587, "collect_steps_per_iteration": 100, "num_iterations": 22}}, {"number": 92, "value": 48.901921623945235, "params": {"learning_rate": 6.759823681041398e-05, "n_layers": 1, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.027273824844219977, "importance_ratio_clipping": 0.1377971614866808, "num_epochs": 6, "lambda_value": 0.9749763730494145, "discount_factor": 0.9897548045951097, "collect_steps_per_iteration": 100, "num_iterations": 21}}, {"number": 93, "value": 50.0, "params": {"learning_rate": 8.849311451101457e-05, "n_layers": 1, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.03726067207014954, "importance_ratio_clipping": 0.11017539556371321, "num_epochs": 5, "lambda_value": 0.9692618759228357, "discount_factor": 0.987960327703322, "collect_steps_per_iteration": 100, "num_iterations": 22}}, {"number": 94, "value": 46.588033330440524, "params": {"learning_rate": 5.0537387000675817e-05, "n_layers": 1, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.03373924530300192, "importance_ratio_clipping": 0.11897364828525431, "num_epochs": 6, "lambda_value": 0.9846662442649265, "discount_factor": 0.9855887931946619, "collect_steps_per_iteration": 100, "num_iterations": 15}}, {"number": 95, "value": 49.233450901508334, "params": {"learning_rate": 0.00013654213029475526, "n_layers": 1, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.018862609460899498, "importance_ratio_clipping": 0.29986440922464613, "num_epochs": 6, "lambda_value": 0.9058043153512471, "discount_factor": 0.9789188753271609, "collect_steps_per_iteration": 150, "num_iterations": 23}}, {"number": 96, "value": 46.363092452287674, "params": {"learning_rate": 8.60403836620996e-05, "n_layers": 1, "layer_size": 128, "activation_fn": "tanh", "entropy_regularization": 0.07184949072060988, "importance_ratio_clipping": 0.15032576375393386, "num_epochs": 7, "lambda_value": 0.9148084068101158, "discount_factor": 0.9664656751741366, "collect_steps_per_iteration": 50, "num_iterations": 13}}, {"number": 97, "value": 50.0, "params": {"learning_rate": 5.657884211658818e-05, "n_layers": 2, "layer_size": 32, "activation_fn": "elu", "entropy_regularization": 0.02276901781922844, "importance_ratio_clipping": 0.13068266771069564, "num_epochs": 5, "lambda_value": 0.9809201063370153, "discount_factor": 0.9956998868168053, "collect_steps_per_iteration": 200, "num_iterations": 22}}, {"number": 98, "value": 49.81446328163147, "params": {"learning_rate": 7.15196414317573e-05, "n_layers": 3, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.08710032712322137, "importance_ratio_clipping": 0.17364435236732603, "num_epochs": 7, "lambda_value": 0.9649419793697012, "discount_factor": 0.9732929889927081, "collect_steps_per_iteration": 100, "num_iterations": 24}}, {"number": 99, "value": 44.47989565730095, "params": {"learning_rate": 0.0001645237212420358, "n_layers": 4, "layer_size": 256, "activation_fn": "tanh", "entropy_regularization": 0.029431445948963384, "importance_ratio_clipping": 0.1004284063845848, "num_epochs": 6, "lambda_value": 0.9273939258079499, "discount_factor": 0.9934002622986965, "collect_steps_per_iteration": 50, "num_iterations": 23}}]}